advanced:
  error_wait_multiplier: 2
  max_consecutive_errors: 5
  max_results_history: 1000
  max_seen_tweets: 10000
  max_wait_time: 1800
anti_block:
  burst_limit: 5
  proxies: []
  requests_per_minute: 30
  use_proxy: false
  user_agents: []
logging:
  backup_count: 5
  console: true
  file: logs/twitter_monitor.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  max_size: 10
monitor:
  accounts:
  - RayXR_
  - maiz_julio
  check_interval: 60
  data_dir: data
  keywords:
  - 安徽
  - 合肥
  max_tweets: 10
output:
  max_text_length: 200
  print_new_tweets: true
  save_csv: false
  save_json: true
scraper:
  headless: true
  max_retries: 3
  request_delay:
  - 2
  - 5
  timeout: 10
