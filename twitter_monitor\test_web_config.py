#!/usr/bin/env python3
"""
测试Web界面配置操作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Config
from web_modules.config_manager import ConfigManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_web_config_manager():
    """测试Web配置管理器"""
    print("🧪 测试Web配置管理器")
    
    try:
        # 初始化配置
        config = Config()
        
        # 初始化Web配置管理器
        config_manager = ConfigManager(config)
        print("✅ Web配置管理器初始化成功")
        
        # 获取初始状态
        initial_accounts = config.get_accounts()
        initial_keywords = config.get_keywords()
        print(f"📋 初始账号: {initial_accounts}")
        print(f"📋 初始关键词: {initial_keywords}")
        
        # 测试添加账号
        test_account = "web_test_account"
        print(f"\n➕ 通过Web管理器添加账号: {test_account}")
        
        # 模拟Web界面的添加操作
        success = config_manager.add_account(test_account)
        if success:
            print("✅ Web添加账号成功")
            
            # 验证账号是否真的添加了
            updated_accounts = config.get_accounts()
            if test_account in updated_accounts:
                print("✅ 账号确实已添加到配置中")
            else:
                print("❌ 账号未添加到配置中")
                return False
        else:
            print("❌ Web添加账号失败")
            return False
        
        # 测试删除账号
        print(f"\n🗑️ 通过Web管理器删除账号: {test_account}")
        success = config_manager.remove_account(test_account)
        if success:
            print("✅ Web删除账号成功")
            
            # 验证账号是否真的删除了
            updated_accounts = config.get_accounts()
            if test_account not in updated_accounts:
                print("✅ 账号确实已从配置中删除")
            else:
                print("❌ 账号未从配置中删除")
                return False
        else:
            print("❌ Web删除账号失败")
            return False
        
        # 测试添加关键词
        test_keyword = "Web测试关键词"
        print(f"\n➕ 通过Web管理器添加关键词: {test_keyword}")
        
        success = config_manager.add_keyword(test_keyword)
        if success:
            print("✅ Web添加关键词成功")
            
            # 验证关键词是否真的添加了
            updated_keywords = config.get_keywords()
            if test_keyword in updated_keywords:
                print("✅ 关键词确实已添加到配置中")
            else:
                print("❌ 关键词未添加到配置中")
                return False
        else:
            print("❌ Web添加关键词失败")
            return False
        
        # 测试删除关键词
        print(f"\n🗑️ 通过Web管理器删除关键词: {test_keyword}")
        success = config_manager.remove_keyword(test_keyword)
        if success:
            print("✅ Web删除关键词成功")
            
            # 验证关键词是否真的删除了
            updated_keywords = config.get_keywords()
            if test_keyword not in updated_keywords:
                print("✅ 关键词确实已从配置中删除")
            else:
                print("❌ 关键词未从配置中删除")
                return False
        else:
            print("❌ Web删除关键词失败")
            return False
        
        print("\n🎉 Web配置管理器测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ Web配置管理器测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_warnings():
    """测试Streamlit警告处理"""
    print("\n🧪 测试Streamlit警告处理")
    
    try:
        # 模拟在非Streamlit环境中调用
        config = Config()
        config_manager = ConfigManager(config)
        
        # 这些操作会产生Streamlit警告，但应该仍然工作
        print("⚠️ 以下操作会产生Streamlit警告，这是正常的（因为不在Streamlit环境中）")
        
        success1 = config_manager.add_account("warning_test_account")
        success2 = config_manager.remove_account("warning_test_account")
        
        if success1 and success2:
            print("✅ 即使有Streamlit警告，操作仍然成功")
            return True
        else:
            print("❌ Streamlit警告影响了操作")
            return False
            
    except Exception as e:
        print(f"❌ Streamlit警告测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Web界面配置操作测试")
    print("=" * 50)
    
    # 运行测试
    test1_passed = test_web_config_manager()
    test2_passed = test_streamlit_warnings()
    
    print("\n" + "=" * 50)
    print("📋 测试结果:")
    print(f"  Web配置管理器测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  Streamlit警告测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！Web界面配置操作功能正常！")
        print("\n💡 如果在实际Web界面中仍有问题，可能是以下原因:")
        print("   1. 浏览器缓存问题 - 尝试刷新页面")
        print("   2. Streamlit状态问题 - 尝试重启Web应用")
        print("   3. 输入框焦点问题 - 确保点击输入框后再点击按钮")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
