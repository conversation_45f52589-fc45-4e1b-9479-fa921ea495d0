#!/usr/bin/env python3
"""
Twitter监测脚本测试程序
用于验证各个模块的功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from core import TwitterScraper, AntiBlockManager, TwitterMonitor
from utils import setup_logger


def test_config():
    """测试配置模块"""
    print("🧪 测试配置模块...")
    
    try:
        config = Config()
        
        print(f"  ✅ 配置加载成功")
        print(f"  📱 监测账号: {config.get_accounts()}")
        print(f"  🔍 监测关键词: {config.get_keywords()}")
        print(f"  ⏱️ 检查间隔: {config.get('monitor.check_interval')}秒")
        
        # 测试添加账号
        config.add_account("test_user")
        config.add_keyword("测试关键词")
        
        print(f"  ✅ 动态配置测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置模块测试失败: {e}")
        return False


def test_logger():
    """测试日志模块"""
    print("\n🧪 测试日志模块...")
    
    try:
        logger = setup_logger('test_logger')
        
        logger.debug("这是调试信息")
        logger.info("这是信息")
        logger.warning("这是警告")
        logger.error("这是错误")
        
        print("  ✅ 日志模块测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 日志模块测试失败: {e}")
        return False


def test_anti_block():
    """测试反屏蔽模块"""
    print("\n🧪 测试反屏蔽模块...")
    
    try:
        config = {
            'requests_per_minute': 60,
            'burst_limit': 10,
            'proxies': []
        }
        
        manager = AntiBlockManager(config)
        
        # 测试请求配置
        request_config = manager.prepare_request()
        print(f"  ✅ 请求配置生成成功")
        print(f"  🔧 User-Agent: {request_config['headers']['User-Agent'][:50]}...")
        
        # 测试状态
        status = manager.get_status()
        print(f"  📊 代理数量: {status['config']['proxy_count']}")
        print(f"  📈 请求统计: {status['stats']['total_requests']}")
        
        manager.cleanup()
        print("  ✅ 反屏蔽模块测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 反屏蔽模块测试失败: {e}")
        return False


def test_scraper_init():
    """测试爬虫初始化"""
    print("\n🧪 测试爬虫初始化...")
    
    try:
        config = {
            'request_delay': [1, 2],
            'max_retries': 2,
            'timeout': 5
        }
        
        scraper = TwitterScraper(config)
        
        # 测试状态
        status = scraper.get_status()
        print(f"  📊 爬虫状态: {status}")
        
        print("  ✅ 爬虫初始化测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 爬虫初始化测试失败: {e}")
        return False


def test_monitor_init():
    """测试监测器初始化"""
    print("\n🧪 测试监测器初始化...")
    
    try:
        config = {
            'accounts': ['test_user'],
            'keywords': ['test'],
            'check_interval': 60,
            'max_tweets': 5,
            'data_dir': 'test_data',
            'scraper': {
                'request_delay': [1, 2],
                'max_retries': 2
            },
            'anti_block': {
                'requests_per_minute': 60,
                'burst_limit': 10
            }
        }
        
        monitor = TwitterMonitor(config)
        
        # 测试状态
        status = monitor.get_status()
        print(f"  📊 监测器状态: 运行中={status['is_running']}")
        print(f"  📱 监测账号: {len(status['accounts'])}个")
        print(f"  🔍 监测关键词: {len(status['keywords'])}个")
        
        monitor.cleanup()
        print("  ✅ 监测器初始化测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 监测器初始化测试失败: {e}")
        return False


def test_selenium_availability():
    """测试Selenium可用性"""
    print("\n🧪 测试Selenium可用性...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        print("  🔍 尝试启动Chrome WebDriver...")
        driver = webdriver.Chrome(options=options)
        
        print("  🌐 测试访问网页...")
        driver.get("https://www.google.com")
        title = driver.title
        
        driver.quit()
        
        print(f"  ✅ Selenium测试成功，页面标题: {title}")
        return True
        
    except Exception as e:
        print(f"  ❌ Selenium测试失败: {e}")
        print("  💡 请确保已安装Chrome浏览器和ChromeDriver")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🐦 Twitter监测脚本测试套件")
    print("=" * 50)
    
    tests = [
        ("配置模块", test_config),
        ("日志模块", test_logger),
        ("反屏蔽模块", test_anti_block),
        ("爬虫初始化", test_scraper_init),
        ("监测器初始化", test_monitor_init),
        ("Selenium可用性", test_selenium_availability)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！脚本可以正常运行")
        return True
    else:
        print("⚠️ 部分测试失败，可能影响功能")
        return False


def main():
    """主函数"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n✅ 测试完成，可以运行主程序:")
            print("   python main.py")
        else:
            print("\n❌ 测试未完全通过，请检查环境配置")
            
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
