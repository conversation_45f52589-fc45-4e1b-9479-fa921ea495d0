"""
监控面板模块
实现实时状态显示和最新推文展示
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import pandas as pd


class Dashboard:
    """监控面板类"""
    
    def __init__(self):
        pass
    
    def render_realtime_status(self):
        """渲染实时监测状态"""
        st.markdown("### 📊 实时监测状态")
        
        # 创建两列布局
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # 监测活动图表
            self.render_activity_chart()
        
        with col2:
            # 状态指示器
            self.render_status_indicators()
    
    def render_activity_chart(self):
        """渲染活动图表"""
        # 模拟监测活动数据
        now = datetime.now()
        times = [now - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        
        # 从session state获取实际数据或使用模拟数据
        if 'monitor_results' in st.session_state and st.session_state.monitor_results:
            # 使用真实数据
            activity_data = self.process_activity_data(st.session_state.monitor_results)
        else:
            # 使用模拟数据
            activity_data = {
                'time': times,
                'tweets': [2, 1, 3, 0, 1, 4, 2, 1, 0, 2, 3, 1],
                'accounts': [1, 1, 2, 0, 1, 2, 1, 1, 0, 1, 2, 1]
            }
        
        # 创建图表
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=activity_data['time'],
            y=activity_data['tweets'],
            mode='lines+markers',
            name='推文数量',
            line=dict(color='#1DA1F2', width=3),
            marker=dict(size=8)
        ))
        
        fig.add_trace(go.Scatter(
            x=activity_data['time'],
            y=activity_data['accounts'],
            mode='lines+markers',
            name='活跃账号',
            line=dict(color='#17BF63', width=3),
            marker=dict(size=8),
            yaxis='y2'
        ))
        
        fig.update_layout(
            title="📈 监测活动趋势",
            xaxis_title="时间",
            yaxis_title="推文数量",
            yaxis2=dict(
                title="活跃账号",
                overlaying='y',
                side='right'
            ),
            height=300,
            showlegend=True,
            hovermode='x unified'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def process_activity_data(self, results):
        """处理活动数据"""
        # 这里可以根据实际的监测结果处理数据
        # 简化处理，返回基本结构
        now = datetime.now()
        times = [now - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        
        return {
            'time': times,
            'tweets': [len(result.get('tweets', [])) for result in results[-12:]] + [0] * (12 - len(results)),
            'accounts': [1 if result.get('tweets') else 0 for result in results[-12:]] + [0] * (12 - len(results))
        }
    
    def render_status_indicators(self):
        """渲染状态指示器"""
        st.markdown("#### 🚦 系统状态")
        
        # 监测状态
        if st.session_state.get('monitor_running', False):
            st.markdown("🟢 **监测服务**: 运行中")
        else:
            st.markdown("🔴 **监测服务**: 已停止")
        
        # 网络状态
        st.markdown("🟢 **网络连接**: 正常")
        
        # 数据库状态
        st.markdown("🟢 **数据存储**: 正常")
        
        # Chrome状态
        st.markdown("🟢 **Chrome驱动**: 就绪")
        
        st.markdown("---")
        
        # 性能指标
        st.markdown("#### ⚡ 性能指标")
        
        # 内存使用率（模拟）
        memory_usage = 65
        st.progress(memory_usage / 100)
        st.text(f"内存使用: {memory_usage}%")
        
        # CPU使用率（模拟）
        cpu_usage = 25
        st.progress(cpu_usage / 100)
        st.text(f"CPU使用: {cpu_usage}%")
    
    def render_latest_tweets(self, results):
        """渲染最新推文"""
        st.markdown("### 📝 最新推文")
        
        if not results:
            st.info("暂无监测结果，请启动监测或刷新数据")
            return
        
        # 获取最新的推文
        latest_tweets = []
        for result in reversed(results[-5:]):  # 最近5次监测结果
            tweets = result.get('tweets', [])
            for tweet in tweets[:3]:  # 每次结果最多显示3条
                latest_tweets.append({
                    'monitor_type': result.get('monitor_type', 'unknown'),
                    'target': result.get('target', 'unknown'),
                    'timestamp': result.get('timestamp', ''),
                    **tweet
                })
        
        if not latest_tweets:
            st.info("最近的监测中未发现新推文")
            return
        
        # 显示推文卡片
        for i, tweet in enumerate(latest_tweets[:10]):  # 最多显示10条
            with st.container():
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    # 推文内容
                    st.markdown(f"""
                    <div class="tweet-card">
                        <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                            <strong>@{tweet.get('author_username', 'unknown')}</strong>
                            <span style="margin-left: 1rem; color: #657786; font-size: 0.9rem;">
                                {tweet.get('created_at', '')[:16]}
                            </span>
                        </div>
                        <p style="margin-bottom: 0.5rem;">{tweet.get('text', '')[:200]}{'...' if len(tweet.get('text', '')) > 200 else ''}</p>
                        <div style="display: flex; gap: 1rem; color: #657786; font-size: 0.9rem;">
                            <span>❤️ {tweet.get('like_count', 0)}</span>
                            <span>🔄 {tweet.get('retweet_count', 0)}</span>
                            <span>💬 {tweet.get('reply_count', 0)}</span>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
                
                with col2:
                    # 监测信息
                    monitor_type = tweet.get('monitor_type', 'unknown')
                    target = tweet.get('target', 'unknown')
                    
                    if monitor_type == 'account':
                        st.markdown(f"📱 **账号监测**\n\n目标: @{target}")
                    elif monitor_type == 'keyword':
                        st.markdown(f"🔍 **关键词监测**\n\n关键词: {target}")
                    else:
                        st.markdown(f"📊 **监测类型**\n\n{monitor_type}")
                    
                    # 操作按钮
                    if st.button(f"📤 推送", key=f"push_{i}"):
                        self.push_tweet_to_dingtalk(tweet)
        
        # 查看更多按钮
        if len(latest_tweets) >= 10:
            if st.button("📋 查看更多推文"):
                st.session_state.page = "📊 结果展示"
                st.rerun()
    
    def push_tweet_to_dingtalk(self, tweet):
        """推送推文到钉钉"""
        try:
            # 这里调用钉钉推送功能
            from .dingtalk_bot import DingTalkBot
            bot = DingTalkBot()
            
            success = bot.send_tweet_notification(tweet)
            if success:
                st.success("✅ 推文已推送到钉钉")
            else:
                st.error("❌ 推送失败，请检查钉钉配置")
                
        except Exception as e:
            st.error(f"❌ 推送失败: {e}")
    
    def render_quick_stats(self):
        """渲染快速统计"""
        st.markdown("### 📊 今日统计")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("今日推文", 45, delta=12)
        
        with col2:
            st.metric("活跃账号", 8, delta=2)
        
        with col3:
            st.metric("关键词命中", 23, delta=5)
