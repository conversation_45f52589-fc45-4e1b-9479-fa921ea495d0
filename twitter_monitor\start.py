#!/usr/bin/env python3
"""
Twitter监测脚本快速启动器
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def check_chrome():
    """检查Chrome浏览器"""
    try:
        # 尝试导入selenium
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # 尝试创建Chrome驱动（不启动浏览器）
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ Chrome浏览器或ChromeDriver检查失败: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        return False


def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    
    try:
        # 检查requirements.txt是否存在
        req_file = Path(__file__).parent / "requirements.txt"
        if not req_file.exists():
            print("❌ 找不到requirements.txt文件")
            return False
        
        # 安装依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(req_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装依赖包时发生错误: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    directories = ['data', 'logs', 'data/exports']
    
    for directory in directories:
        dir_path = Path(__file__).parent / directory
        dir_path.mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")


def check_config():
    """检查配置文件"""
    config_file = Path(__file__).parent / "config" / "config.yaml"
    
    if not config_file.exists():
        print("❌ 配置文件不存在，将使用默认配置")
        return False
    
    print("✅ 配置文件检查通过")
    return True


def main():
    """主函数"""
    print("🐦 Twitter监测脚本启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查并安装依赖
    try:
        import selenium
        import requests
        import yaml
        print("✅ 核心依赖包已安装")
    except ImportError:
        print("📦 检测到缺少依赖包，正在安装...")
        if not install_dependencies():
            print("❌ 依赖包安装失败，请手动运行: pip install -r requirements.txt")
            return
    
    # 创建目录
    create_directories()
    
    # 检查配置
    check_config()
    
    # 检查Chrome（可选）
    print("🔍 检查Chrome浏览器...")
    if not check_chrome():
        print("⚠️ Chrome检查失败，但仍可尝试运行")
    else:
        print("✅ Chrome浏览器检查通过")
    
    print("\n🚀 准备启动Twitter监测脚本...")
    print("=" * 50)
    
    # 启动主程序
    try:
        from main import main as run_main
        run_main()
    except ImportError as e:
        print(f"❌ 导入主程序失败: {e}")
        print("请确保所有文件都在正确位置")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


if __name__ == "__main__":
    main()
