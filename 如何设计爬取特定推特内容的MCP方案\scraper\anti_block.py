"""
反屏蔽管理器
实现代理轮换、User-Agent轮换、请求频率控制等反屏蔽策略
"""

import random
import time
import asyncio
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import logging
from fake_useragent import UserAgent
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)


@dataclass
class ProxyConfig:
    """代理配置"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"  # http, https, socks4, socks5
    
    def to_dict(self) -> Dict[str, str]:
        """转换为requests可用的代理格式"""
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"
        else:
            auth = ""
        
        proxy_url = f"{self.protocol}://{auth}{self.host}:{self.port}"
        return {
            "http": proxy_url,
            "https": proxy_url
        }


class ProxyRotator:
    """代理轮换器"""
    
    def __init__(self, proxies: List[ProxyConfig] = None):
        self.proxies = proxies or []
        self.current_index = 0
        self.failed_proxies = set()
        self.last_rotation_time = 0
        self.rotation_interval = 300  # 5分钟轮换一次
    
    def add_proxy(self, proxy: ProxyConfig):
        """添加代理"""
        self.proxies.append(proxy)
        logger.info(f"添加代理: {proxy.host}:{proxy.port}")
    
    def get_current_proxy(self) -> Optional[Dict[str, str]]:
        """获取当前代理"""
        if not self.proxies:
            return None
        
        # 检查是否需要轮换
        current_time = time.time()
        if (current_time - self.last_rotation_time) > self.rotation_interval:
            self.rotate()
        
        available_proxies = [p for i, p in enumerate(self.proxies) 
                           if i not in self.failed_proxies]
        
        if not available_proxies:
            # 重置失败代理列表
            self.failed_proxies.clear()
            available_proxies = self.proxies
        
        if available_proxies:
            proxy = available_proxies[self.current_index % len(available_proxies)]
            return proxy.to_dict()
        
        return None
    
    def rotate(self):
        """轮换代理"""
        if self.proxies:
            self.current_index = (self.current_index + 1) % len(self.proxies)
            self.last_rotation_time = time.time()
            logger.debug(f"代理轮换到索引: {self.current_index}")
    
    def mark_failed(self, proxy_dict: Dict[str, str]):
        """标记代理失败"""
        for i, proxy in enumerate(self.proxies):
            if proxy.to_dict() == proxy_dict:
                self.failed_proxies.add(i)
                logger.warning(f"标记代理失败: {proxy.host}:{proxy.port}")
                break
    
    def get_status(self) -> Dict[str, Any]:
        """获取代理状态"""
        return {
            "total_proxies": len(self.proxies),
            "failed_proxies": len(self.failed_proxies),
            "current_index": self.current_index,
            "last_rotation_time": self.last_rotation_time
        }


class UserAgentRotator:
    """User-Agent轮换器"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.custom_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
        ]
        self.last_agent = None
    
    def get_random_agent(self) -> str:
        """获取随机User-Agent"""
        try:
            # 70%概率使用fake-useragent，30%概率使用自定义
            if random.random() < 0.7:
                agent = self.ua.random
            else:
                agent = random.choice(self.custom_agents)
            
            # 避免连续使用相同的User-Agent
            if agent == self.last_agent and len(self.custom_agents) > 1:
                agent = random.choice([a for a in self.custom_agents if a != agent])
            
            self.last_agent = agent
            return agent
        except Exception as e:
            logger.warning(f"获取随机User-Agent失败: {e}")
            return random.choice(self.custom_agents)


class RateLimiter:
    """请求频率限制器"""
    
    def __init__(self, requests_per_minute: int = 30, burst_limit: int = 5):
        self.requests_per_minute = requests_per_minute
        self.burst_limit = burst_limit
        self.request_times = []
        self.burst_count = 0
        self.last_burst_reset = time.time()
    
    async def wait_if_needed(self):
        """如果需要则等待"""
        current_time = time.time()
        
        # 清理过期的请求记录
        self.request_times = [t for t in self.request_times 
                            if current_time - t < 60]
        
        # 检查突发限制
        if current_time - self.last_burst_reset > 60:
            self.burst_count = 0
            self.last_burst_reset = current_time
        
        # 如果超过突发限制，等待
        if self.burst_count >= self.burst_limit:
            wait_time = 60 - (current_time - self.last_burst_reset)
            if wait_time > 0:
                logger.debug(f"突发限制: 等待 {wait_time:.2f} 秒")
                await asyncio.sleep(wait_time)
                self.burst_count = 0
                self.last_burst_reset = time.time()
        
        # 检查每分钟限制
        if len(self.request_times) >= self.requests_per_minute:
            wait_time = 60 - (current_time - self.request_times[0])
            if wait_time > 0:
                logger.debug(f"频率限制: 等待 {wait_time:.2f} 秒")
                await asyncio.sleep(wait_time)
        
        # 记录请求时间
        self.request_times.append(time.time())
        self.burst_count += 1
        
        # 添加随机延迟，模拟人类行为
        random_delay = random.uniform(1, 3)
        await asyncio.sleep(random_delay)


class SessionManager:
    """会话管理器"""
    
    def __init__(self, proxy_rotator: ProxyRotator = None, 
                 ua_rotator: UserAgentRotator = None):
        self.proxy_rotator = proxy_rotator or ProxyRotator()
        self.ua_rotator = ua_rotator or UserAgentRotator()
        self.sessions = {}
        self.session_count = 0
        self.max_sessions = 5
    
    def get_session(self, session_id: str = None) -> requests.Session:
        """获取配置好的会话"""
        if session_id is None:
            session_id = f"session_{self.session_count}"
            self.session_count += 1
        
        if session_id in self.sessions:
            return self.sessions[session_id]
        
        # 创建新会话
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置代理
        proxy = self.proxy_rotator.get_current_proxy()
        if proxy:
            session.proxies.update(proxy)
        
        # 设置User-Agent
        user_agent = self.ua_rotator.get_random_agent()
        session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 缓存会话
        self.sessions[session_id] = session
        
        # 限制会话数量
        if len(self.sessions) > self.max_sessions:
            oldest_session = min(self.sessions.keys())
            self.sessions[oldest_session].close()
            del self.sessions[oldest_session]
        
        logger.debug(f"创建新会话: {session_id}")
        return session
    
    def close_session(self, session_id: str):
        """关闭指定会话"""
        if session_id in self.sessions:
            self.sessions[session_id].close()
            del self.sessions[session_id]
            logger.debug(f"关闭会话: {session_id}")
    
    def close_all_sessions(self):
        """关闭所有会话"""
        for session in self.sessions.values():
            session.close()
        self.sessions.clear()
        logger.info("已关闭所有会话")


class AntiBlockManager:
    """反屏蔽管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 初始化组件
        self.proxy_rotator = ProxyRotator()
        self.ua_rotator = UserAgentRotator()
        self.rate_limiter = RateLimiter(
            requests_per_minute=self.config.get('requests_per_minute', 30),
            burst_limit=self.config.get('burst_limit', 5)
        )
        self.session_manager = SessionManager(self.proxy_rotator, self.ua_rotator)
        
        # 屏蔽检测
        self.block_indicators = [
            'rate limit',
            'too many requests',
            'temporarily restricted',
            'account suspended',
            'access denied',
            'blocked',
            'forbidden'
        ]
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'blocked_requests': 0,
            'proxy_rotations': 0,
            'last_block_time': 0
        }
    
    def add_proxies_from_list(self, proxy_list: List[str]):
        """从字符串列表添加代理"""
        for proxy_str in proxy_list:
            try:
                # 解析代理字符串 (格式: protocol://host:port 或 host:port)
                if '://' in proxy_str:
                    protocol, address = proxy_str.split('://', 1)
                else:
                    protocol = 'http'
                    address = proxy_str
                
                if '@' in address:
                    auth, host_port = address.split('@', 1)
                    username, password = auth.split(':', 1)
                else:
                    username = password = None
                    host_port = address
                
                host, port = host_port.split(':', 1)
                
                proxy_config = ProxyConfig(
                    host=host,
                    port=int(port),
                    username=username,
                    password=password,
                    protocol=protocol
                )
                
                self.proxy_rotator.add_proxy(proxy_config)
                
            except Exception as e:
                logger.warning(f"解析代理失败 {proxy_str}: {e}")
    
    async def make_request(self, url: str, method: str = 'GET', 
                          session_id: str = None, **kwargs) -> requests.Response:
        """发送请求（带反屏蔽保护）"""
        # 频率限制
        await self.rate_limiter.wait_if_needed()
        
        # 获取会话
        session = self.session_manager.get_session(session_id)
        
        # 更新统计
        self.stats['total_requests'] += 1
        
        try:
            # 发送请求
            response = session.request(method, url, **kwargs)
            
            # 检查是否被屏蔽
            if self._is_blocked(response):
                self.stats['blocked_requests'] += 1
                self.stats['last_block_time'] = time.time()
                
                # 处理屏蔽
                await self._handle_block(session_id)
                
                # 重试请求
                session = self.session_manager.get_session(session_id)
                response = session.request(method, url, **kwargs)
            
            return response
            
        except Exception as e:
            logger.error(f"请求失败 {url}: {e}")
            
            # 如果是代理相关错误，标记代理失败
            if 'proxy' in str(e).lower():
                current_proxy = self.proxy_rotator.get_current_proxy()
                if current_proxy:
                    self.proxy_rotator.mark_failed(current_proxy)
            
            raise
    
    def _is_blocked(self, response: requests.Response) -> bool:
        """检测是否被屏蔽"""
        # 检查状态码
        if response.status_code in [429, 403, 401]:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        for indicator in self.block_indicators:
            if indicator in content:
                return True
        
        return False
    
    async def _handle_block(self, session_id: str = None):
        """处理屏蔽"""
        logger.warning("检测到屏蔽，执行反屏蔽策略")
        
        # 轮换代理
        self.proxy_rotator.rotate()
        self.stats['proxy_rotations'] += 1
        
        # 关闭当前会话，强制创建新会话
        if session_id:
            self.session_manager.close_session(session_id)
        
        # 等待一段时间
        wait_time = random.uniform(30, 60)
        logger.info(f"等待 {wait_time:.2f} 秒后继续")
        await asyncio.sleep(wait_time)
    
    def get_status(self) -> Dict[str, Any]:
        """获取反屏蔽管理器状态"""
        return {
            "proxy_status": self.proxy_rotator.get_status(),
            "session_count": len(self.session_manager.sessions),
            "stats": self.stats,
            "rate_limiter": {
                "requests_per_minute": self.rate_limiter.requests_per_minute,
                "current_requests": len(self.rate_limiter.request_times),
                "burst_count": self.rate_limiter.burst_count
            }
        }
    
    def cleanup(self):
        """清理资源"""
        self.session_manager.close_all_sessions()
        logger.info("反屏蔽管理器已清理")


# 测试代码
if __name__ == "__main__":
    async def test_anti_block():
        manager = AntiBlockManager()
        
        # 添加一些测试代理（注意：这些是示例代理，实际使用时需要有效代理）
        test_proxies = [
            "http://proxy1.example.com:8080",
            "http://proxy2.example.com:8080",
        ]
        manager.add_proxies_from_list(test_proxies)
        
        # 获取状态
        status = manager.get_status()
        print("反屏蔽管理器状态:", status)
        
        # 清理
        manager.cleanup()
    
    # 运行测试
    asyncio.run(test_anti_block())

