#!/bin/bash

# Twitter MCP Scraper 启动脚本
# 用于快速启动和管理服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_DIR"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    local python_version=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $python_version"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    # 检查关键依赖
    python3 -c "import twikit, flask, yaml" 2>/dev/null || {
        log_warning "部分依赖缺失，正在安装..."
        pip3 install -r requirements.txt
    }
    
    log_success "依赖检查完成"
}

# 检查配置
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "config.yaml" ]; then
        log_warning "配置文件不存在，创建默认配置..."
        cp config.yaml.example config.yaml 2>/dev/null || {
            log_error "无法创建配置文件"
            exit 1
        }
    fi
    
    log_success "配置检查完成"
}

# 运行测试
run_tests() {
    log_info "运行基本测试..."
    
    if python3 test_basic.py; then
        log_success "所有测试通过"
    else
        log_error "测试失败，请检查配置"
        exit 1
    fi
}

# 启动服务
start_service() {
    log_info "启动Twitter MCP爬虫服务..."
    
    # 创建必要的目录
    mkdir -p logs data
    
    # 启动服务
    export PYTHONPATH="$PROJECT_DIR/src"
    python3 src/main.py
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    
    local pid=$(pgrep -f "python3 src/main.py" || true)
    if [ -n "$pid" ]; then
        kill "$pid"
        log_success "服务已停止"
    else
        log_warning "服务未运行"
    fi
}

# 查看状态
show_status() {
    log_info "检查服务状态..."
    
    local pid=$(pgrep -f "python3 src/main.py" || true)
    if [ -n "$pid" ]; then
        log_success "服务正在运行 (PID: $pid)"
        
        # 尝试访问状态接口
        if command -v curl &> /dev/null; then
            curl -s http://localhost:8000/status | python3 -m json.tool 2>/dev/null || {
                log_warning "无法访问状态接口"
            }
        fi
    else
        log_warning "服务未运行"
    fi
}

# 查看日志
show_logs() {
    log_info "显示最近日志..."
    
    if [ -f "logs/twitter-mcp.log" ]; then
        tail -n 50 logs/twitter-mcp.log
    else
        log_warning "日志文件不存在"
    fi
}

# Docker相关操作
docker_build() {
    log_info "构建Docker镜像..."
    docker build -t twitter-mcp-scraper .
    log_success "Docker镜像构建完成"
}

docker_run() {
    log_info "运行Docker容器..."
    docker-compose up -d
    log_success "Docker容器已启动"
}

docker_stop() {
    log_info "停止Docker容器..."
    docker-compose down
    log_success "Docker容器已停止"
}

# 显示帮助
show_help() {
    echo "Twitter MCP Scraper 管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看状态"
    echo "  logs        查看日志"
    echo "  test        运行测试"
    echo "  docker-build 构建Docker镜像"
    echo "  docker-run   运行Docker容器"
    echo "  docker-stop  停止Docker容器"
    echo "  help        显示帮助"
    echo ""
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            check_python
            check_dependencies
            check_config
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            stop_service
            sleep 2
            check_python
            check_dependencies
            check_config
            start_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        test)
            check_python
            check_dependencies
            check_config
            run_tests
            ;;
        docker-build)
            docker_build
            ;;
        docker-run)
            docker_run
            ;;
        docker-stop)
            docker_stop
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

