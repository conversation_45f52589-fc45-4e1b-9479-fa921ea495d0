"""
日志配置模块
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Dict, Any


def setup_logger(name: str = None, config: Dict[str, Any] = None) -> logging.Logger:
    """设置日志记录器"""
    
    # 默认配置
    default_config = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file': 'logs/twitter_monitor.log',
        'max_size': 10,  # MB
        'backup_count': 5,
        'console': True
    }
    
    # 合并配置
    if config:
        default_config.update(config)
    
    # 获取日志记录器
    logger_name = name or 'twitter_monitor'
    logger = logging.getLogger(logger_name)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    # 设置日志级别
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    log_level = level_map.get(default_config['level'].upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 创建格式化器
    formatter = logging.Formatter(default_config['format'])
    
    # 文件处理器
    if default_config['file']:
        log_file = Path(default_config['file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler实现日志轮转
        max_bytes = default_config['max_size'] * 1024 * 1024  # 转换为字节
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=default_config['backup_count'],
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 控制台处理器
    if default_config['console']:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return logging.getLogger(name)


# 测试代码
if __name__ == "__main__":
    # 测试日志配置
    test_config = {
        'level': 'DEBUG',
        'file': 'test_logs/test.log',
        'console': True
    }
    
    logger = setup_logger('test_logger', test_config)
    
    logger.debug("这是调试信息")
    logger.info("这是信息")
    logger.warning("这是警告")
    logger.error("这是错误")
    
    print("日志测试完成")
