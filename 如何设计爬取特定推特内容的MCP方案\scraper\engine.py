"""
Twitter爬虫引擎
基于twikit库实现的Twitter数据爬取功能
"""

import asyncio
import json
import time
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging

# 第三方库
try:
    from twikit import Client
    from twikit.errors import BadToken, TooManyRequests, Forbidden
except ImportError:
    print("警告: twikit库未安装，请运行 pip install twikit")
    Client = None

from fake_useragent import UserAgent
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class TweetData:
    """推文数据结构"""
    id: str
    text: str
    author_username: str
    author_display_name: str
    author_followers_count: int
    author_verified: bool
    created_at: str
    retweet_count: int
    like_count: int
    reply_count: int
    quote_count: int
    hashtags: List[str]
    mentions: List[str]
    urls: List[str]
    media: List[Dict[str, str]]
    language: Optional[str] = None
    is_retweet: bool = False
    is_reply: bool = False


@dataclass
class UserData:
    """用户数据结构"""
    username: str
    display_name: str
    description: str
    followers_count: int
    following_count: int
    tweets_count: int
    verified: bool
    created_at: str
    profile_image_url: str
    banner_url: Optional[str] = None


class TwitterScraper:
    """Twitter爬虫核心类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.client = None
        self.session = None
        self.ua = UserAgent()
        self.is_logged_in = False
        self.last_request_time = 0
        self.request_count = 0
        self.daily_request_limit = 1000
        
        # 初始化HTTP会话
        self._init_session()
    
    def _init_session(self):
        """初始化HTTP会话"""
        self.session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认headers
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
    
    async def initialize(self, username: str = None, password: str = None, 
                        cookies_path: str = None):
        """初始化Twitter客户端"""
        if not Client:
            raise ImportError("twikit库未安装")
        
        try:
            self.client = Client('zh-CN')
            
            # 尝试加载已保存的cookies
            if cookies_path:
                try:
                    self.client.load_cookies(cookies_path)
                    self.is_logged_in = True
                    logger.info("已加载保存的cookies")
                    return True
                except Exception as e:
                    logger.warning(f"加载cookies失败: {e}")
            
            # 如果提供了用户名和密码，则登录
            if username and password:
                await self._login(username, password, cookies_path)
                return True
            
            logger.warning("未提供登录信息，某些功能可能受限")
            return False
            
        except Exception as e:
            logger.error(f"初始化Twitter客户端失败: {e}")
            raise
    
    async def _login(self, username: str, password: str, cookies_path: str = None):
        """登录Twitter"""
        try:
            # 添加随机延迟，模拟人类行为
            await asyncio.sleep(random.uniform(1, 3))
            
            self.client.login(
                auth_info_1=username,
                password=password
            )
            
            self.is_logged_in = True
            logger.info("Twitter登录成功")
            
            # 保存cookies
            if cookies_path:
                self.client.save_cookies(cookies_path)
                logger.info(f"cookies已保存到: {cookies_path}")
                
        except Exception as e:
            logger.error(f"Twitter登录失败: {e}")
            raise
    
    async def scrape_user_tweets(self, username: str, count: int = 20, 
                               include_replies: bool = False, 
                               max_age_days: int = 30) -> List[TweetData]:
        """爬取用户推文"""
        if not self.client:
            raise RuntimeError("Twitter客户端未初始化")
        
        try:
            # 速率限制
            await self._rate_limit()
            
            # 获取用户信息
            user = self.client.get_user_by_screen_name(username)
            logger.info(f"开始爬取用户 @{username} 的推文")
            
            # 获取推文
            tweet_type = 'Tweets' if not include_replies else 'Replies'
            tweets = user.get_tweets(tweet_type, count=count)
            
            # 转换为标准格式
            tweet_data_list = []
            cutoff_date = datetime.now() - timedelta(days=max_age_days)
            
            for tweet in tweets:
                # 检查推文时间
                tweet_date = datetime.strptime(tweet.created_at, '%a %b %d %H:%M:%S %z %Y')
                if tweet_date.replace(tzinfo=None) < cutoff_date:
                    continue
                
                tweet_data = self._convert_tweet_to_data(tweet)
                tweet_data_list.append(tweet_data)
            
            logger.info(f"成功爬取 {len(tweet_data_list)} 条推文")
            return tweet_data_list
            
        except TooManyRequests:
            logger.warning("请求过于频繁，等待后重试")
            await asyncio.sleep(60)
            raise
        except Forbidden:
            logger.error(f"无权访问用户 @{username} 的推文（可能是私人账户）")
            raise
        except Exception as e:
            logger.error(f"爬取用户推文失败: {e}")
            raise
    
    async def scrape_keyword_tweets(self, keyword: str, count: int = 20,
                                  language: str = None, 
                                  date_range: str = "30d") -> List[TweetData]:
        """根据关键词搜索推文"""
        if not self.client:
            raise RuntimeError("Twitter客户端未初始化")
        
        try:
            # 速率限制
            await self._rate_limit()
            
            # 构建搜索查询
            query = keyword
            if language:
                query += f" lang:{language}"
            
            logger.info(f"开始搜索关键词: {query}")
            
            # 执行搜索
            tweets = self.client.search_tweet(query, product='Latest', count=count)
            
            # 转换为标准格式
            tweet_data_list = []
            for tweet in tweets:
                tweet_data = self._convert_tweet_to_data(tweet)
                tweet_data_list.append(tweet_data)
            
            logger.info(f"成功搜索到 {len(tweet_data_list)} 条推文")
            return tweet_data_list
            
        except TooManyRequests:
            logger.warning("搜索请求过于频繁，等待后重试")
            await asyncio.sleep(60)
            raise
        except Exception as e:
            logger.error(f"搜索推文失败: {e}")
            raise
    
    async def get_user_info(self, username: str) -> UserData:
        """获取用户信息"""
        if not self.client:
            raise RuntimeError("Twitter客户端未初始化")
        
        try:
            # 速率限制
            await self._rate_limit()
            
            user = self.client.get_user_by_screen_name(username)
            
            user_data = UserData(
                username=user.screen_name,
                display_name=user.name,
                description=user.description or "",
                followers_count=user.followers_count,
                following_count=user.friends_count,
                tweets_count=user.statuses_count,
                verified=user.verified,
                created_at=user.created_at,
                profile_image_url=user.profile_image_url_https,
                banner_url=getattr(user, 'profile_banner_url', None)
            )
            
            logger.info(f"成功获取用户 @{username} 的信息")
            return user_data
            
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            raise
    
    def _convert_tweet_to_data(self, tweet) -> TweetData:
        """将twikit推文对象转换为标准数据格式"""
        try:
            # 提取hashtags
            hashtags = []
            if hasattr(tweet, 'entities') and 'hashtags' in tweet.entities:
                hashtags = [tag['text'] for tag in tweet.entities['hashtags']]
            
            # 提取mentions
            mentions = []
            if hasattr(tweet, 'entities') and 'user_mentions' in tweet.entities:
                mentions = [mention['screen_name'] for mention in tweet.entities['user_mentions']]
            
            # 提取URLs
            urls = []
            if hasattr(tweet, 'entities') and 'urls' in tweet.entities:
                urls = [url['expanded_url'] for url in tweet.entities['urls']]
            
            # 提取媒体
            media = []
            if hasattr(tweet, 'entities') and 'media' in tweet.entities:
                for media_item in tweet.entities['media']:
                    media.append({
                        'type': media_item['type'],
                        'url': media_item['media_url_https']
                    })
            
            return TweetData(
                id=tweet.id,
                text=tweet.full_text,
                author_username=tweet.user.screen_name,
                author_display_name=tweet.user.name,
                author_followers_count=tweet.user.followers_count,
                author_verified=tweet.user.verified,
                created_at=tweet.created_at,
                retweet_count=tweet.retweet_count,
                like_count=tweet.favorite_count,
                reply_count=getattr(tweet, 'reply_count', 0),
                quote_count=getattr(tweet, 'quote_count', 0),
                hashtags=hashtags,
                mentions=mentions,
                urls=urls,
                media=media,
                language=getattr(tweet, 'lang', None),
                is_retweet=hasattr(tweet, 'retweeted_status'),
                is_reply=tweet.in_reply_to_status_id is not None
            )
        except Exception as e:
            logger.error(f"转换推文数据失败: {e}")
            # 返回基本数据
            return TweetData(
                id=getattr(tweet, 'id', 'unknown'),
                text=getattr(tweet, 'full_text', ''),
                author_username=getattr(tweet.user, 'screen_name', 'unknown'),
                author_display_name=getattr(tweet.user, 'name', 'unknown'),
                author_followers_count=getattr(tweet.user, 'followers_count', 0),
                author_verified=getattr(tweet.user, 'verified', False),
                created_at=getattr(tweet, 'created_at', ''),
                retweet_count=getattr(tweet, 'retweet_count', 0),
                like_count=getattr(tweet, 'favorite_count', 0),
                reply_count=0,
                quote_count=0,
                hashtags=[],
                mentions=[],
                urls=[],
                media=[]
            )
    
    async def _rate_limit(self):
        """实现速率限制"""
        current_time = time.time()
        
        # 检查请求间隔
        if self.last_request_time > 0:
            time_diff = current_time - self.last_request_time
            min_interval = 2  # 最小间隔2秒
            
            if time_diff < min_interval:
                sleep_time = min_interval - time_diff + random.uniform(0.5, 1.5)
                logger.debug(f"速率限制: 等待 {sleep_time:.2f} 秒")
                await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
        
        # 检查每日请求限制
        if self.request_count >= self.daily_request_limit:
            logger.warning("已达到每日请求限制")
            raise Exception("已达到每日请求限制")
    
    def get_status(self) -> Dict[str, Any]:
        """获取爬虫状态"""
        return {
            "is_initialized": self.client is not None,
            "is_logged_in": self.is_logged_in,
            "request_count": self.request_count,
            "daily_limit": self.daily_request_limit,
            "last_request_time": self.last_request_time,
            "session_active": self.session is not None
        }
    
    async def close(self):
        """关闭爬虫，清理资源"""
        if self.session:
            self.session.close()
        logger.info("Twitter爬虫已关闭")


# 测试代码
if __name__ == "__main__":
    async def test_scraper():
        scraper = TwitterScraper()
        
        # 测试状态
        status = scraper.get_status()
        print("爬虫状态:", json.dumps(status, indent=2, ensure_ascii=False))
        
        # 注意：实际使用时需要提供真实的登录信息
        # await scraper.initialize("your_username", "your_password")
        
    # 运行测试
    asyncio.run(test_scraper())

