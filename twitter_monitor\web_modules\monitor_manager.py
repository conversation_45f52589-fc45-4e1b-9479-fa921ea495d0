"""
监测管理器模块
负责与后端监测逻辑的交互
"""

import threading
import time
import logging
from typing import List, Dict, Any
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core import TwitterMonitor
from config import Config

logger = logging.getLogger(__name__)


class MonitorManager:
    """监测管理器"""
    
    def __init__(self):
        self.config = Config()
        self.monitor = None
        self.monitor_thread = None
        self.is_running = False
        self.results_cache = []
        self.status_cache = {
            'success_count': 0,
            'error_count': 0,
            'accounts_monitored': 0,
            'keywords_monitored': 0,
            'last_check_time': None
        }
        
        # 初始化监测器
        self._init_monitor()
    
    def _init_monitor(self):
        """初始化监测器"""
        try:
            monitor_config = {
                'accounts': self.config.get_accounts(),
                'keywords': self.config.get_keywords(),
                'check_interval': self.config.get('monitor.check_interval', 300),
                'max_tweets': self.config.get('monitor.max_tweets', 20),
                'data_dir': self.config.get('monitor.data_dir', 'data'),
                'scraper': self.config.scraper,
                'anti_block': self.config.anti_block
            }
            
            self.monitor = TwitterMonitor(monitor_config)
            
            # 添加结果回调
            self.monitor.add_result_callback(self._handle_monitor_result)
            
            # 更新状态缓存
            self.status_cache.update({
                'accounts_monitored': len(self.config.get_accounts()),
                'keywords_monitored': len(self.config.get_keywords())
            })
            
            logger.info("监测管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化监测管理器失败: {e}")
            raise
    
    def _handle_monitor_result(self, result):
        """处理监测结果"""
        try:
            # 转换结果为字典格式
            result_dict = {
                'monitor_type': result.monitor_type,
                'target': result.target,
                'tweets': [tweet.__dict__ for tweet in result.tweets],
                'timestamp': result.timestamp,
                'success': result.success,
                'error_message': result.error_message
            }
            
            # 添加到结果缓存
            self.results_cache.append(result_dict)
            
            # 限制缓存大小
            if len(self.results_cache) > 100:
                self.results_cache = self.results_cache[-50:]
            
            # 更新状态统计
            if result.success:
                self.status_cache['success_count'] += 1
            else:
                self.status_cache['error_count'] += 1
            
            self.status_cache['last_check_time'] = datetime.now().isoformat()
            
            logger.info(f"处理监测结果: {result.monitor_type} - {result.target}, 推文数: {len(result.tweets)}")
            
        except Exception as e:
            logger.error(f"处理监测结果失败: {e}")
    
    def start_monitoring(self):
        """启动监测"""
        try:
            if self.is_running:
                logger.warning("监测已在运行中")
                return False

            if not self.monitor:
                self._init_monitor()

            # 初始化监测器
            if self.monitor.initialize():
                # 启动监测
                self.monitor.start_monitoring()
                self.is_running = True

                # 更新session state
                import streamlit as st
                if 'monitor_status' not in st.session_state:
                    st.session_state.monitor_status = {}
                st.session_state.monitor_status['is_running'] = True
                st.session_state.monitor_status['last_update'] = datetime.now().strftime('%H:%M:%S')

                logger.info("监测已启动")
                return True
            else:
                logger.error("监测器初始化失败")
                return False

        except Exception as e:
            logger.error(f"启动监测失败: {e}")
            return False
    
    def stop_monitoring(self):
        """停止监测"""
        try:
            if not self.is_running:
                logger.warning("监测未在运行")
                return False

            if self.monitor:
                self.monitor.stop_monitoring()

            self.is_running = False

            # 更新session state
            import streamlit as st
            if 'monitor_status' not in st.session_state:
                st.session_state.monitor_status = {}
            st.session_state.monitor_status['is_running'] = False
            st.session_state.monitor_status['last_update'] = datetime.now().strftime('%H:%M:%S')

            logger.info("监测已停止")
            return True

        except Exception as e:
            logger.error(f"停止监测失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取监测状态"""
        try:
            base_status = {
                'is_running': self.is_running,
                'monitor_initialized': self.monitor is not None,
                **self.status_cache
            }
            
            # 如果监测器存在，获取详细状态
            if self.monitor:
                monitor_status = self.monitor.get_status()
                base_status.update(monitor_status)
            
            return base_status
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {
                'is_running': False,
                'error': str(e),
                **self.status_cache
            }
    
    def get_recent_results(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的监测结果"""
        try:
            return self.results_cache[-limit:] if self.results_cache else []
        except Exception as e:
            logger.error(f"获取最近结果失败: {e}")
            return []
    
    def get_all_results(self) -> List[Dict[str, Any]]:
        """获取所有监测结果"""
        return self.results_cache.copy()
    
    def clear_results(self):
        """清空结果缓存"""
        self.results_cache.clear()
        logger.info("结果缓存已清空")
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        try:
            # 停止当前监测
            was_running = self.is_running
            if was_running:
                self.stop_monitoring()
            
            # 更新配置
            for key, value in new_config.items():
                if key == 'accounts':
                    # 清空现有账号并添加新账号
                    current_accounts = self.config.get_accounts()
                    for account in current_accounts:
                        self.config.remove_account(account)
                    for account in value:
                        self.config.add_account(account)
                
                elif key == 'keywords':
                    # 清空现有关键词并添加新关键词
                    current_keywords = self.config.get_keywords()
                    for keyword in current_keywords:
                        self.config.remove_keyword(keyword)
                    for keyword in value:
                        self.config.add_keyword(keyword)
                
                else:
                    # 其他配置项
                    self.config.set(key, value)
            
            # 保存配置
            self.config.save_config()
            
            # 重新初始化监测器
            self._init_monitor()
            
            # 如果之前在运行，重新启动
            if was_running:
                self.start_monitoring()
            
            logger.info("配置更新完成")
            return True
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        try:
            if not self.monitor:
                self._init_monitor()
            
            # 测试爬虫初始化
            test_result = {
                'scraper_init': False,
                'chrome_available': False,
                'network_connection': False,
                'config_valid': False
            }
            
            # 测试配置
            if self.config.get_accounts() or self.config.get_keywords():
                test_result['config_valid'] = True
            
            # 测试Chrome
            try:
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                
                options = Options()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                
                driver = webdriver.Chrome(options=options)
                driver.get("https://www.google.com")
                driver.quit()
                
                test_result['chrome_available'] = True
                test_result['network_connection'] = True
                
            except Exception as e:
                logger.warning(f"Chrome测试失败: {e}")
            
            # 测试爬虫初始化
            try:
                if self.monitor.initialize():
                    test_result['scraper_init'] = True
            except Exception as e:
                logger.warning(f"爬虫初始化测试失败: {e}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return {
                'error': str(e),
                'scraper_init': False,
                'chrome_available': False,
                'network_connection': False,
                'config_valid': False
            }
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.is_running:
                self.stop_monitoring()
            
            if self.monitor:
                self.monitor.cleanup()
            
            logger.info("监测管理器清理完成")
            
        except Exception as e:
            logger.error(f"清理监测管理器失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
