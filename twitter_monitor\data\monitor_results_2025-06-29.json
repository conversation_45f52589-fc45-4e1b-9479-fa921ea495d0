[{"monitor_type": "account", "target": "elonmusk", "tweets": [{"id": "tweet", "text": "The latest Senate draft bill will destroy millions of jobs in America and cause immense strategic harm to our country!\n\nUtterly insane and destructive. It gives handouts to industries of the past while severely damaging industries of the future.", "author_username": "elonmusk", "author_display_name": "<PERSON><PERSON>", "created_at": "2025-06-28T20:01:00.000Z", "retweet_count": 0, "like_count": 0, "reply_count": 0, "hashtags": [], "mentions": [], "urls": [], "is_retweet": false, "is_reply": false}], "tweet_count": 1, "timestamp": "2025-06-29T11:16:58.151941", "success": true, "error_message": null}, {"monitor_type": "account", "target": "elonmusk", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:17:57.054520", "success": true, "error_message": null}, {"monitor_type": "account", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:47:05.232801", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "人工智能", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:47:28.815120", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:47:53.959775", "success": true, "error_message": null}, {"monitor_type": "account", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:48:03.823785", "success": true, "error_message": null}, {"monitor_type": "account", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:48:04.838393", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:48:20.956154", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "人工智能", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:48:29.377164", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "人工智能", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:48:36.677522", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:48:58.896690", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:49:05.718814", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:49:27.377139", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:49:30.804636", "success": true, "error_message": null}, {"monitor_type": "account", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:50:45.470176", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "人工智能", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:51:11.212891", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:51:39.324687", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T11:52:08.394535", "success": true, "error_message": null}, {"monitor_type": "account", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:17:01.105723", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "人工智能", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:17:30.203908", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:17:57.333124", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:18:24.388801", "success": true, "error_message": null}, {"monitor_type": "account", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:22:55.935425", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "人工智能", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:23:19.068089", "success": true, "error_message": null}, {"monitor_type": "account", "target": "RayXR_", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:34:52.696157", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "人工智能", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:35:16.044355", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:35:42.511124", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:36:08.244597", "success": true, "error_message": null}, {"monitor_type": "account", "target": "RayXR_", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:36:44.552821", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:37:11.154439", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:37:35.194764", "success": true, "error_message": null}, {"monitor_type": "account", "target": "RayXR_", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:39:04.627271", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "安徽", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:39:30.094012", "success": true, "error_message": null}, {"monitor_type": "account", "target": "RayXR_", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:39:44.754719", "success": true, "error_message": null}, {"monitor_type": "keyword", "target": "合肥", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:39:58.881997", "success": true, "error_message": null}, {"monitor_type": "account", "target": "maiz_julio", "tweets": [], "tweet_count": 0, "timestamp": "2025-06-29T12:40:19.172790", "success": true, "error_message": null}]