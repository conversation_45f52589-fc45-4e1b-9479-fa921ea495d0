# Twitter账号监测脚本

一个不依赖Twitter API的账号监测脚本，能够监测指定账号和关键词，并返回相关推文。

## 🌟 功能特性

- **🚫 无API依赖**: 完全基于网页爬虫技术，无需Twitter API
- **👤 账号监测**: 监测指定用户的最新推文
- **🔍 关键词监测**: 搜索包含特定关键词的推文
- **🛡️ 反屏蔽策略**: 代理轮换、User-Agent轮换、请求频率控制
- **⏰ 定时监测**: 支持定时检查和结果输出
- **💾 数据导出**: 支持JSON、CSV格式导出
- **📊 实时显示**: 实时打印新发现的推文
- **🔧 灵活配置**: 支持YAML配置文件，易于定制

## 📋 系统要求

- Python 3.8+
- Chrome浏览器
- ChromeDriver（自动安装）
- 稳定的网络连接

## 🚀 快速开始

### 方法一：自动安装（推荐）

```bash
# 1. 下载项目
git clone <repository-url>
cd twitter_monitor

# 2. 运行自动安装器
python install.py

# 3. 启动监测
python main.py
```

### 方法二：手动安装

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 创建目录
mkdir -p data logs data/exports

# 3. 配置设置（编辑 config/config.yaml）
# 4. 运行脚本
python main.py
```

### 方法三：使用启动器

```bash
python start.py
```

## ⚙️ 配置说明

编辑 `config/config.yaml` 文件：

```yaml
# 监测配置
monitor:
  accounts: ["elonmusk", "openai"]  # 要监测的账号
  keywords: ["人工智能", "AI"]      # 要监测的关键词
  check_interval: 300              # 检查间隔（秒）
  max_tweets: 20                   # 每次获取的最大推文数

# 反屏蔽配置
anti_block:
  requests_per_minute: 30          # 每分钟请求数限制
  burst_limit: 5                   # 突发请求限制
  proxies: []                      # 代理列表（可选）
    # - "http://proxy1.example.com:8080"
    # - "socks5://proxy2.example.com:1080"

# 输出配置
output:
  print_new_tweets: true           # 是否实时打印新推文
  save_json: true                  # 是否保存JSON文件
  save_csv: false                  # 是否保存CSV文件
```

## 📁 项目结构

```
twitter_monitor/
├── config/              # 配置文件
│   ├── __init__.py
│   ├── config.yaml      # 主配置文件
│   └── settings.py      # 配置管理类
├── core/                # 核心模块
│   ├── __init__.py
│   ├── scraper.py       # 爬虫引擎
│   ├── anti_block.py    # 反屏蔽策略
│   └── monitor.py       # 监测逻辑
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── logger.py        # 日志配置
│   └── helpers.py       # 辅助函数
├── data/                # 数据存储
│   ├── exports/         # 导出文件
│   └── monitor_results_*.json
├── logs/                # 日志文件
├── requirements.txt     # 依赖包列表
├── install.py          # 自动安装器
├── start.py            # 启动器
├── main.py             # 主程序
└── README.md           # 说明文档
```

## 🎯 使用方法

### 基本使用

```bash
# 启动监测
python main.py

# 添加监测账号
python main.py --add-account "username"

# 添加监测关键词
python main.py --add-keyword "关键词"

# 查看状态
python main.py --status

# 测试配置
python main.py --test
```

### 高级使用

```bash
# 使用自定义配置文件
python main.py --config custom_config.yaml

# 移除监测目标
python main.py --remove-account "username"
python main.py --remove-keyword "关键词"
```

## 📊 输出示例

当发现新推文时，脚本会实时显示：

```
🔔 发现新推文 - ACCOUNT: elonmusk
📅 时间: 2024-01-01T12:00:00
📊 数量: 3 条
================================================================================

1. @elonmusk (Elon Musk)
   🕒 2024-01-01T12:00:00Z
   💬 Just had a great conversation about AI safety...
   📈 ❤️1234 🔄567 💬89
   🏷️ #AI #Safety

2. @elonmusk (Elon Musk)
   🕒 2024-01-01T11:30:00Z
   💬 Working on something exciting at @Tesla...
   📈 ❤️2345 🔄890 💬123
   👥 @Tesla

================================================================================
```

## 🔧 反屏蔽功能

### 代理支持

```yaml
anti_block:
  proxies:
    - "http://proxy1.example.com:8080"
    - "http://username:<EMAIL>:8080"
    - "socks5://proxy3.example.com:1080"
```

### 频率控制

- 自动控制请求频率，避免触发限制
- 支持突发请求限制
- 随机延迟模拟人类行为

### User-Agent轮换

- 自动轮换User-Agent
- 支持自定义User-Agent列表
- 避免连续使用相同标识

## 📈 监测策略

### 账号监测

- 定期检查指定用户的最新推文
- 自动过滤已见过的推文
- 支持包含/排除回复推文

### 关键词监测

- 实时搜索包含关键词的推文
- 支持多语言关键词
- 智能去重避免重复推文

### 数据持久化

- 自动保存监测结果到JSON文件
- 可选导出CSV格式
- 保存已见推文ID避免重复

## 🛠️ 故障排除

### 常见问题

1. **ChromeDriver错误**
   ```bash
   # 重新安装ChromeDriver
   python -c "from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"
   ```

2. **依赖包错误**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --force-reinstall
   ```

3. **权限错误**
   ```bash
   # Linux/Mac 添加执行权限
   chmod +x start.sh
   ```

### 日志分析

查看详细日志：
```bash
tail -f logs/twitter_monitor.log
```

日志级别说明：
- `DEBUG`: 详细调试信息
- `INFO`: 一般操作信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息

### 性能优化

1. **调整检查间隔**
   - 增加 `check_interval` 减少请求频率
   - 建议最少300秒（5分钟）

2. **使用代理池**
   - 配置多个代理提高稳定性
   - 定期轮换代理避免封禁

3. **限制推文数量**
   - 减少 `max_tweets` 降低负载
   - 建议不超过50条

## 🔒 安全注意事项

### 使用建议

1. **遵守法律法规**
   - 遵守当地法律法规
   - 遵守Twitter服务条款
   - 尊重用户隐私权

2. **合理使用**
   - 控制爬取频率
   - 仅爬取公开内容
   - 避免过度请求

3. **数据安全**
   - 定期清理敏感日志
   - 加密存储配置文件
   - 限制网络访问权限

### 风险提示

- 过度使用可能导致IP被封
- 违反服务条款可能面临法律风险
- 网站结构变化可能导致功能失效

## 🤝 贡献指南

欢迎贡献代码和建议：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本工具仅供学习和研究使用。使用者应当：

1. 遵守当地法律法规
2. 遵守Twitter服务条款
3. 尊重用户隐私权
4. 合理使用爬虫功能
5. 承担使用风险和责任

作者不对使用本工具造成的任何后果承担责任。

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 加入讨论群

---

**版本**: 1.0.0
**更新时间**: 2024年12月
**作者**: AI Assistant
