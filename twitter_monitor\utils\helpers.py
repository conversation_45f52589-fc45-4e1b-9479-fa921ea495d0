"""
辅助工具函数
"""

import csv
import json
import re
from typing import List, Dict, Any
from datetime import datetime
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


def format_tweet_text(text: str, max_length: int = 200) -> str:
    """格式化推文文本"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 截断文本
    if len(text) > max_length:
        text = text[:max_length] + "..."
    
    return text


def clean_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    # 移除或替换非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    cleaned = re.sub(illegal_chars, '_', filename)
    
    # 移除多余的下划线
    cleaned = re.sub(r'_+', '_', cleaned)
    
    # 移除开头和结尾的下划线
    cleaned = cleaned.strip('_')
    
    return cleaned


def export_to_json(data: List[Dict[str, Any]], filepath: str) -> bool:
    """导出数据到JSON文件"""
    try:
        file_path = Path(filepath)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"数据已导出到JSON文件: {filepath}")
        return True
        
    except Exception as e:
        logger.error(f"导出JSON文件失败: {e}")
        return False


def export_to_csv(data: List[Dict[str, Any]], filepath: str, 
                  fieldnames: List[str] = None) -> bool:
    """导出数据到CSV文件"""
    try:
        if not data:
            logger.warning("没有数据可导出")
            return False
        
        file_path = Path(filepath)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 如果没有指定字段名，使用第一条记录的键
        if not fieldnames:
            fieldnames = list(data[0].keys())
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for row in data:
                # 处理嵌套字典和列表
                processed_row = {}
                for key, value in row.items():
                    if isinstance(value, (dict, list)):
                        processed_row[key] = json.dumps(value, ensure_ascii=False)
                    else:
                        processed_row[key] = str(value) if value is not None else ""
                
                writer.writerow(processed_row)
        
        logger.info(f"数据已导出到CSV文件: {filepath}")
        return True
        
    except Exception as e:
        logger.error(f"导出CSV文件失败: {e}")
        return False


def flatten_tweet_data(tweet_data: Dict[str, Any]) -> Dict[str, Any]:
    """扁平化推文数据，便于CSV导出"""
    flattened = {}
    
    # 基本信息
    flattened['id'] = tweet_data.get('id', '')
    flattened['text'] = tweet_data.get('text', '')
    flattened['created_at'] = tweet_data.get('created_at', '')
    
    # 作者信息
    author = tweet_data.get('author', {})
    flattened['author_username'] = author.get('username', '')
    flattened['author_display_name'] = author.get('display_name', '')
    flattened['author_followers_count'] = author.get('followers_count', 0)
    flattened['author_verified'] = author.get('verified', False)
    
    # 互动数据
    metrics = tweet_data.get('metrics', {})
    flattened['retweet_count'] = metrics.get('retweet_count', 0)
    flattened['like_count'] = metrics.get('like_count', 0)
    flattened['reply_count'] = metrics.get('reply_count', 0)
    flattened['quote_count'] = metrics.get('quote_count', 0)
    
    # 内容信息
    content = tweet_data.get('content', {})
    flattened['hashtags'] = ', '.join(content.get('hashtags', []))
    flattened['mentions'] = ', '.join(content.get('mentions', []))
    flattened['urls'] = ', '.join(content.get('urls', []))
    
    # 元数据
    metadata = tweet_data.get('metadata', {})
    flattened['language'] = metadata.get('language', '')
    flattened['is_retweet'] = metadata.get('is_retweet', False)
    flattened['is_reply'] = metadata.get('is_reply', False)
    
    return flattened


def export_tweets_to_csv(tweets: List[Dict[str, Any]], filepath: str) -> bool:
    """导出推文数据到CSV文件"""
    try:
        if not tweets:
            logger.warning("没有推文数据可导出")
            return False
        
        # 扁平化推文数据
        flattened_tweets = [flatten_tweet_data(tweet) for tweet in tweets]
        
        # 定义CSV字段顺序
        fieldnames = [
            'id', 'text', 'created_at',
            'author_username', 'author_display_name', 'author_followers_count', 'author_verified',
            'retweet_count', 'like_count', 'reply_count', 'quote_count',
            'hashtags', 'mentions', 'urls',
            'language', 'is_retweet', 'is_reply'
        ]
        
        return export_to_csv(flattened_tweets, filepath, fieldnames)
        
    except Exception as e:
        logger.error(f"导出推文CSV文件失败: {e}")
        return False


def generate_report_filename(monitor_type: str, target: str, 
                           timestamp: datetime = None) -> str:
    """生成报告文件名"""
    if timestamp is None:
        timestamp = datetime.now()
    
    date_str = timestamp.strftime('%Y%m%d_%H%M%S')
    clean_target = clean_filename(target)
    
    return f"{monitor_type}_{clean_target}_{date_str}"


def parse_time_range(time_str: str) -> int:
    """解析时间范围字符串，返回秒数"""
    time_str = time_str.lower().strip()
    
    # 匹配数字和单位
    match = re.match(r'(\d+)\s*([smhd])', time_str)
    if not match:
        return 0
    
    value = int(match.group(1))
    unit = match.group(2)
    
    multipliers = {
        's': 1,          # 秒
        'm': 60,         # 分钟
        'h': 3600,       # 小时
        'd': 86400       # 天
    }
    
    return value * multipliers.get(unit, 1)


def format_duration(seconds: int) -> str:
    """格式化持续时间"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes}分钟"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分钟"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}天{hours}小时"


def validate_username(username: str) -> bool:
    """验证Twitter用户名格式"""
    if not username:
        return False
    
    # 移除@符号
    username = username.lstrip('@')
    
    # Twitter用户名规则：1-15个字符，只能包含字母、数字和下划线
    pattern = r'^[a-zA-Z0-9_]{1,15}$'
    return bool(re.match(pattern, username))


def sanitize_keyword(keyword: str) -> str:
    """清理关键词"""
    if not keyword:
        return ""
    
    # 移除多余的空白字符
    keyword = re.sub(r'\s+', ' ', keyword.strip())
    
    # 移除特殊字符（保留中文、英文、数字、空格、#、@）
    keyword = re.sub(r'[^\w\s#@\u4e00-\u9fff]', '', keyword)
    
    return keyword


def get_file_size(filepath: str) -> int:
    """获取文件大小（字节）"""
    try:
        return Path(filepath).stat().st_size
    except:
        return 0


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


# 测试代码
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 测试推文数据扁平化
    test_tweet = {
        'id': '123456789',
        'text': '这是一条测试推文 #AI #测试',
        'created_at': '2024-01-01T12:00:00Z',
        'author': {
            'username': 'testuser',
            'display_name': '测试用户',
            'followers_count': 1000,
            'verified': True
        },
        'metrics': {
            'retweet_count': 10,
            'like_count': 50,
            'reply_count': 5,
            'quote_count': 2
        },
        'content': {
            'hashtags': ['AI', '测试'],
            'mentions': ['user1'],
            'urls': ['https://example.com']
        },
        'metadata': {
            'language': 'zh',
            'is_retweet': False,
            'is_reply': False
        }
    }
    
    flattened = flatten_tweet_data(test_tweet)
    print("扁平化推文数据:")
    for key, value in flattened.items():
        print(f"  {key}: {value}")
    
    # 测试文件名清理
    dirty_filename = "用户@test<user>推文_2024/01/01"
    clean_name = clean_filename(dirty_filename)
    print(f"\n清理前: {dirty_filename}")
    print(f"清理后: {clean_name}")
    
    # 测试时间解析
    time_ranges = ['30s', '5m', '2h', '1d']
    for time_range in time_ranges:
        seconds = parse_time_range(time_range)
        formatted = format_duration(seconds)
        print(f"{time_range} = {seconds}秒 = {formatted}")
    
    # 测试用户名验证
    usernames = ['valid_user', 'invalid-user', '@test_user', 'toolongusername123456']
    for username in usernames:
        is_valid = validate_username(username)
        print(f"用户名 '{username}': {'有效' if is_valid else '无效'}")
