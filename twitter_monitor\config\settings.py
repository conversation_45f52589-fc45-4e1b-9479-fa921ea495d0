"""
配置管理类
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "config/config.yaml"
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            config_file = Path(self.config_path)
            
            if not config_file.exists():
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                self.config_data = self._get_default_config()
                return
            
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)
            
            logger.info(f"成功加载配置文件: {self.config_path}")
            
            # 验证配置
            self._validate_config()
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.info("使用默认配置")
            self.config_data = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'monitor': {
                'accounts': ['elonmusk'],
                'keywords': ['AI', '人工智能'],
                'check_interval': 300,
                'max_tweets': 20,
                'data_dir': 'data'
            },
            'scraper': {
                'request_delay': [2, 5],
                'max_retries': 3,
                'timeout': 10,
                'headless': True
            },
            'anti_block': {
                'requests_per_minute': 30,
                'burst_limit': 5,
                'use_proxy': False,
                'proxies': [],
                'user_agents': []
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'logs/twitter_monitor.log',
                'max_size': 10,
                'backup_count': 5,
                'console': True
            },
            'output': {
                'print_new_tweets': True,
                'save_json': True,
                'save_csv': False,
                'max_text_length': 200
            },
            'advanced': {
                'max_seen_tweets': 10000,
                'max_results_history': 1000,
                'max_consecutive_errors': 5,
                'error_wait_multiplier': 2,
                'max_wait_time': 1800
            }
        }
    
    def _validate_config(self):
        """验证配置"""
        try:
            # 检查必需的配置项
            required_sections = ['monitor', 'scraper', 'anti_block', 'logging']
            for section in required_sections:
                if section not in self.config_data:
                    logger.warning(f"缺少配置节: {section}")
                    self.config_data[section] = self._get_default_config()[section]
            
            # 验证监测配置
            monitor_config = self.config_data['monitor']
            if not monitor_config.get('accounts') and not monitor_config.get('keywords'):
                logger.warning("未配置监测账号和关键词，将使用默认配置")
                monitor_config['accounts'] = ['elonmusk']
                monitor_config['keywords'] = ['AI']
            
            # 验证数值配置
            if monitor_config.get('check_interval', 0) < 60:
                logger.warning("检查间隔过短，调整为60秒")
                monitor_config['check_interval'] = 60
            
            if monitor_config.get('max_tweets', 0) > 100:
                logger.warning("最大推文数过大，调整为100")
                monitor_config['max_tweets'] = 100
            
            logger.info("配置验证完成")
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value):
        """设置配置值"""
        keys = key.split('.')
        config = self.config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, path: str = None):
        """保存配置到文件"""
        try:
            save_path = path or self.config_path
            save_file = Path(save_path)
            
            # 确保目录存在
            save_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(save_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"配置已保存到: {save_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    @property
    def monitor(self) -> Dict[str, Any]:
        """监测配置"""
        return self.config_data.get('monitor', {})
    
    @property
    def scraper(self) -> Dict[str, Any]:
        """爬虫配置"""
        return self.config_data.get('scraper', {})
    
    @property
    def anti_block(self) -> Dict[str, Any]:
        """反屏蔽配置"""
        return self.config_data.get('anti_block', {})
    
    @property
    def logging_config(self) -> Dict[str, Any]:
        """日志配置"""
        return self.config_data.get('logging', {})
    
    @property
    def output(self) -> Dict[str, Any]:
        """输出配置"""
        return self.config_data.get('output', {})
    
    @property
    def advanced(self) -> Dict[str, Any]:
        """高级配置"""
        return self.config_data.get('advanced', {})
    
    def get_accounts(self) -> List[str]:
        """获取监测账号列表"""
        return self.monitor.get('accounts', [])
    
    def get_keywords(self) -> List[str]:
        """获取监测关键词列表"""
        return self.monitor.get('keywords', [])
    
    def add_account(self, account: str):
        """添加监测账号"""
        accounts = self.get_accounts()
        if account not in accounts:
            accounts.append(account)
            self.set('monitor.accounts', accounts)
            logger.info(f"添加监测账号: @{account}")
    
    def remove_account(self, account: str):
        """移除监测账号"""
        accounts = self.get_accounts()
        if account in accounts:
            accounts.remove(account)
            self.set('monitor.accounts', accounts)
            logger.info(f"移除监测账号: @{account}")
    
    def add_keyword(self, keyword: str):
        """添加监测关键词"""
        keywords = self.get_keywords()
        if keyword not in keywords:
            keywords.append(keyword)
            self.set('monitor.keywords', keywords)
            logger.info(f"添加监测关键词: {keyword}")
    
    def remove_keyword(self, keyword: str):
        """移除监测关键词"""
        keywords = self.get_keywords()
        if keyword in keywords:
            keywords.remove(keyword)
            self.set('monitor.keywords', keywords)
            logger.info(f"移除监测关键词: {keyword}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.config_data.copy()
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Config(accounts={self.get_accounts()}, keywords={self.get_keywords()})"


# 测试代码
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 测试配置管理
    config = Config()
    
    print("当前配置:")
    print(f"监测账号: {config.get_accounts()}")
    print(f"监测关键词: {config.get_keywords()}")
    print(f"检查间隔: {config.get('monitor.check_interval')}秒")
    
    # 测试添加账号和关键词
    config.add_account("testuser")
    config.add_keyword("测试关键词")
    
    print("\n更新后配置:")
    print(f"监测账号: {config.get_accounts()}")
    print(f"监测关键词: {config.get_keywords()}")
    
    # 测试保存配置
    config.save_config("test_config.yaml")
