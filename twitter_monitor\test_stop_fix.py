#!/usr/bin/env python3
"""
测试停止监测功能修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Config
from web_modules.monitor_manager import MonitorManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_monitor_start_stop():
    """测试监测启动和停止功能"""
    print("🧪 测试监测启动和停止功能")
    
    try:
        # 初始化配置
        config = Config()
        
        # 添加测试账号
        config.add_account("test_account")
        print("✅ 添加测试账号")
        
        # 初始化监测管理器
        monitor_manager = MonitorManager(config)
        print("✅ 初始化监测管理器")
        
        # 测试启动
        print("\n🚀 测试启动监测...")
        if monitor_manager.start_monitoring():
            print("✅ 启动成功")
            
            # 检查状态
            status = monitor_manager.get_status()
            print(f"📊 监测状态: {status}")
            
            # 测试停止
            print("\n⏹️ 测试停止监测...")
            if monitor_manager.stop_monitoring():
                print("✅ 停止成功")
                
                # 再次检查状态
                status = monitor_manager.get_status()
                print(f"📊 监测状态: {status}")
                
                print("\n🎉 所有测试通过！")
                return True
            else:
                print("❌ 停止失败")
                return False
        else:
            print("❌ 启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        # 清理
        try:
            if 'monitor_manager' in locals():
                monitor_manager.cleanup()
            print("🧹 清理完成")
        except:
            pass

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理")
    
    try:
        # 初始化配置（不添加账号和关键词）
        config = Config()
        
        # 初始化监测管理器
        monitor_manager = MonitorManager(config)
        
        # 测试无配置启动
        print("🚀 测试无配置启动...")
        if not monitor_manager.start_monitoring():
            print("✅ 正确拒绝了无配置启动")
        else:
            print("❌ 应该拒绝无配置启动")
            return False
        
        # 测试重复停止
        print("⏹️ 测试重复停止...")
        if not monitor_manager.stop_monitoring():
            print("✅ 正确处理了重复停止")
        else:
            print("⚠️ 停止了未运行的监测")
        
        print("🎉 错误处理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔧 监测功能修复测试")
    print("=" * 50)
    
    # 运行测试
    test1_passed = test_monitor_start_stop()
    test2_passed = test_error_handling()
    
    print("\n" + "=" * 50)
    print("📋 测试结果:")
    print(f"  启动停止测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  错误处理测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！修复成功！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
