"""
钉钉机器人推送模块
实现钉钉群机器人的消息推送功能
"""

import streamlit as st
import requests
import json
import hmac
import hashlib
import base64
import time
from datetime import datetime
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class DingTalkBot:
    """钉钉机器人类"""
    
    def __init__(self):
        self.webhook_url = ""
        self.secret = ""
        self.enabled = False
        self.load_config()
    
    def load_config(self):
        """加载钉钉配置"""
        try:
            # 从session state加载配置
            if 'dingtalk_config' in st.session_state:
                config = st.session_state.dingtalk_config
                self.webhook_url = config.get('webhook_url', '')
                self.secret = config.get('secret', '')
                self.enabled = config.get('enabled', False)
        except Exception as e:
            logger.error(f"加载钉钉配置失败: {e}")
    
    def save_config(self, webhook_url: str, secret: str, enabled: bool):
        """保存钉钉配置"""
        try:
            config = {
                'webhook_url': webhook_url,
                'secret': secret,
                'enabled': enabled
            }
            st.session_state.dingtalk_config = config
            
            self.webhook_url = webhook_url
            self.secret = secret
            self.enabled = enabled
            
            logger.info("钉钉配置已保存")
            return True
            
        except Exception as e:
            logger.error(f"保存钉钉配置失败: {e}")
            return False
    
    def render(self):
        """渲染钉钉推送界面"""
        st.markdown('<h1 class="main-header">🔔 钉钉机器人推送</h1>', unsafe_allow_html=True)
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["⚙️ 机器人配置", "📤 手动推送", "🔄 自动推送", "📊 推送记录"])
        
        with tab1:
            self.render_bot_config()
        
        with tab2:
            self.render_manual_push()
        
        with tab3:
            self.render_auto_push()
        
        with tab4:
            self.render_push_history()
    
    def render_bot_config(self):
        """渲染机器人配置"""
        st.markdown("### ⚙️ 钉钉机器人配置")
        
        # 配置说明
        with st.expander("📖 配置说明"):
            st.markdown("""
            **如何获取钉钉机器人配置:**
            
            1. 在钉钉群中添加自定义机器人
            2. 选择"自定义"机器人类型
            3. 设置机器人名称和头像
            4. 安全设置选择"加签"方式
            5. 复制Webhook地址和加签密钥
            
            **注意事项:**
            - Webhook地址格式: https://oapi.dingtalk.com/robot/send?access_token=xxx
            - 加签密钥用于验证消息来源，提高安全性
            - 机器人每分钟最多发送20条消息
            """)
        
        # 当前配置状态
        col1, col2 = st.columns([3, 1])
        
        with col1:
            if self.enabled and self.webhook_url:
                st.success("🟢 钉钉机器人已配置并启用")
            elif self.webhook_url:
                st.warning("🟡 钉钉机器人已配置但未启用")
            else:
                st.error("🔴 钉钉机器人未配置")
        
        with col2:
            if st.button("🧪 测试连接"):
                self.test_connection()
        
        st.markdown("---")
        
        # 配置表单
        with st.form("dingtalk_config"):
            st.markdown("#### 🔧 机器人配置")
            
            webhook_url = st.text_input(
                "Webhook地址",
                value=self.webhook_url,
                placeholder="https://oapi.dingtalk.com/robot/send?access_token=xxx",
                help="钉钉机器人的Webhook地址"
            )
            
            secret = st.text_input(
                "加签密钥",
                value=self.secret,
                type="password",
                placeholder="SEC开头的密钥字符串",
                help="钉钉机器人的加签密钥，用于消息验证"
            )
            
            enabled = st.checkbox(
                "启用钉钉推送",
                value=self.enabled,
                help="开启后将自动推送监测到的新推文"
            )
            
            # 推送设置
            st.markdown("#### 📤 推送设置")
            
            col1, col2 = st.columns(2)
            
            with col1:
                push_new_tweets = st.checkbox("推送新推文", value=True)
                push_keywords = st.checkbox("推送关键词命中", value=True)
            
            with col2:
                push_errors = st.checkbox("推送错误信息", value=False)
                push_summary = st.checkbox("推送监测摘要", value=True)
            
            # 推送频率限制
            st.markdown("#### ⏱️ 频率限制")
            
            col1, col2 = st.columns(2)
            
            with col1:
                max_messages_per_hour = st.number_input(
                    "每小时最大消息数",
                    min_value=1,
                    max_value=60,
                    value=20,
                    help="限制每小时推送的最大消息数量"
                )
            
            with col2:
                min_interval = st.number_input(
                    "最小推送间隔（秒）",
                    min_value=10,
                    max_value=300,
                    value=30,
                    help="两次推送之间的最小时间间隔"
                )
            
            # 提交按钮
            submitted = st.form_submit_button("💾 保存配置", use_container_width=True)
            
            if submitted:
                if webhook_url and secret:
                    success = self.save_config(webhook_url, secret, enabled)
                    if success:
                        st.success("✅ 钉钉配置已保存")
                        
                        # 保存推送设置
                        push_settings = {
                            'push_new_tweets': push_new_tweets,
                            'push_keywords': push_keywords,
                            'push_errors': push_errors,
                            'push_summary': push_summary,
                            'max_messages_per_hour': max_messages_per_hour,
                            'min_interval': min_interval
                        }
                        st.session_state.dingtalk_push_settings = push_settings
                        
                        st.rerun()
                    else:
                        st.error("❌ 保存配置失败")
                else:
                    st.error("❌ 请填写完整的Webhook地址和密钥")
    
    def render_manual_push(self):
        """渲染手动推送"""
        st.markdown("### 📤 手动推送消息")
        
        if not self.enabled:
            st.warning("⚠️ 钉钉机器人未启用，请先在配置页面启用")
            return
        
        # 消息类型选择
        message_type = st.selectbox(
            "消息类型",
            ["文本消息", "Markdown消息", "链接消息", "推文消息"],
            index=0
        )
        
        if message_type == "文本消息":
            self.render_text_message_form()
        elif message_type == "Markdown消息":
            self.render_markdown_message_form()
        elif message_type == "链接消息":
            self.render_link_message_form()
        elif message_type == "推文消息":
            self.render_tweet_message_form()
    
    def render_text_message_form(self):
        """渲染文本消息表单"""
        with st.form("text_message"):
            st.markdown("#### 📝 文本消息")
            
            content = st.text_area(
                "消息内容",
                placeholder="输入要发送的文本消息...",
                height=150
            )
            
            at_all = st.checkbox("@所有人", value=False)
            
            at_mobiles = st.text_input(
                "@指定人员",
                placeholder="输入手机号，多个用逗号分隔",
                help="例如: 13800138000,13900139000"
            )
            
            if st.form_submit_button("📤 发送消息", use_container_width=True):
                if content:
                    success = self.send_text_message(content, at_all, at_mobiles)
                    if success:
                        st.success("✅ 消息发送成功")
                    else:
                        st.error("❌ 消息发送失败")
                else:
                    st.error("❌ 请输入消息内容")
    
    def render_markdown_message_form(self):
        """渲染Markdown消息表单"""
        with st.form("markdown_message"):
            st.markdown("#### 📝 Markdown消息")
            
            title = st.text_input(
                "消息标题",
                placeholder="输入消息标题..."
            )
            
            content = st.text_area(
                "Markdown内容",
                placeholder="输入Markdown格式的消息内容...",
                height=200,
                help="支持Markdown语法，如 **粗体**、*斜体*、[链接](url) 等"
            )
            
            # Markdown预览
            if content:
                st.markdown("**预览效果:**")
                st.markdown(content)
            
            if st.form_submit_button("📤 发送Markdown消息", use_container_width=True):
                if title and content:
                    success = self.send_markdown_message(title, content)
                    if success:
                        st.success("✅ Markdown消息发送成功")
                    else:
                        st.error("❌ Markdown消息发送失败")
                else:
                    st.error("❌ 请输入标题和内容")
    
    def render_link_message_form(self):
        """渲染链接消息表单"""
        with st.form("link_message"):
            st.markdown("#### 🔗 链接消息")
            
            title = st.text_input("链接标题", placeholder="输入链接标题...")
            text = st.text_area("链接描述", placeholder="输入链接描述...", height=100)
            message_url = st.text_input("链接地址", placeholder="https://example.com")
            pic_url = st.text_input("图片地址", placeholder="https://example.com/image.jpg (可选)")
            
            if st.form_submit_button("📤 发送链接消息", use_container_width=True):
                if title and text and message_url:
                    success = self.send_link_message(title, text, message_url, pic_url)
                    if success:
                        st.success("✅ 链接消息发送成功")
                    else:
                        st.error("❌ 链接消息发送失败")
                else:
                    st.error("❌ 请填写完整的链接信息")
    
    def render_tweet_message_form(self):
        """渲染推文消息表单"""
        st.markdown("#### 🐦 推文消息")
        
        # 从监测结果中选择推文
        if 'monitor_results' in st.session_state and st.session_state.monitor_results:
            # 提取所有推文
            all_tweets = []
            for result in st.session_state.monitor_results:
                tweets = result.get('tweets', [])
                for tweet in tweets:
                    tweet_info = {
                        'display': f"@{tweet.get('author_username', 'unknown')}: {tweet.get('text', '')[:50]}...",
                        'data': {
                            'monitor_type': result.get('monitor_type'),
                            'target': result.get('target'),
                            **tweet
                        }
                    }
                    all_tweets.append(tweet_info)
            
            if all_tweets:
                selected_tweet_index = st.selectbox(
                    "选择要推送的推文",
                    range(len(all_tweets)),
                    format_func=lambda x: all_tweets[x]['display']
                )
                
                selected_tweet = all_tweets[selected_tweet_index]['data']
                
                # 显示推文预览
                st.markdown("**推文预览:**")
                st.markdown(f"**@{selected_tweet.get('author_username')}**: {selected_tweet.get('text')}")
                st.markdown(f"❤️ {selected_tweet.get('like_count', 0)} | 🔄 {selected_tweet.get('retweet_count', 0)} | 💬 {selected_tweet.get('reply_count', 0)}")
                
                if st.button("📤 推送此推文", use_container_width=True):
                    success = self.send_tweet_notification(selected_tweet)
                    if success:
                        st.success("✅ 推文推送成功")
                    else:
                        st.error("❌ 推文推送失败")
            else:
                st.info("暂无推文可推送")
        else:
            st.info("暂无监测结果，请先进行监测")
    
    def render_auto_push(self):
        """渲染自动推送设置"""
        st.markdown("### 🔄 自动推送设置")
        
        # 获取当前推送设置
        push_settings = st.session_state.get('dingtalk_push_settings', {})
        
        # 自动推送规则
        st.markdown("#### 📋 推送规则")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**推送触发条件:**")
            st.checkbox("发现新推文时", value=push_settings.get('push_new_tweets', True), disabled=True)
            st.checkbox("关键词命中时", value=push_settings.get('push_keywords', True), disabled=True)
            st.checkbox("监测出错时", value=push_settings.get('push_errors', False), disabled=True)
        
        with col2:
            st.markdown("**推送频率限制:**")
            st.text(f"每小时最大: {push_settings.get('max_messages_per_hour', 20)} 条")
            st.text(f"最小间隔: {push_settings.get('min_interval', 30)} 秒")
        
        # 推送模板设置
        st.markdown("#### 📝 消息模板")
        
        template_type = st.selectbox(
            "选择模板类型",
            ["新推文通知", "关键词命中", "监测摘要", "错误通知"],
            index=0
        )
        
        if template_type == "新推文通知":
            self.render_tweet_template()
        elif template_type == "关键词命中":
            self.render_keyword_template()
        elif template_type == "监测摘要":
            self.render_summary_template()
        elif template_type == "错误通知":
            self.render_error_template()
    
    def render_push_history(self):
        """渲染推送记录"""
        st.markdown("### 📊 推送记录")
        
        # 推送统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("今日推送", 15, delta=3)
        
        with col2:
            st.metric("成功率", "95%", delta="2%")
        
        with col3:
            st.metric("平均响应", "0.8s", delta="-0.1s")
        
        with col4:
            st.metric("错误次数", 1, delta=-2)
        
        # 推送记录表格
        st.markdown("#### 📋 最近推送记录")
        
        # 示例数据
        history_data = [
            {"时间": "2024-01-01 14:30:25", "类型": "新推文", "内容": "@elonmusk: 关于AI的最新想法...", "状态": "成功", "响应时间": "0.6s"},
            {"时间": "2024-01-01 14:25:10", "类型": "关键词", "内容": "检测到关键词'AI'命中", "状态": "成功", "响应时间": "0.8s"},
            {"时间": "2024-01-01 14:20:05", "类型": "摘要", "内容": "监测摘要报告", "状态": "成功", "响应时间": "1.2s"},
            {"时间": "2024-01-01 14:15:30", "类型": "新推文", "内容": "@openai: ChatGPT更新公告", "状态": "失败", "响应时间": "5.0s"},
        ]
        
        # 状态颜色映射
        def get_status_color(status):
            if status == "成功":
                return "🟢"
            elif status == "失败":
                return "🔴"
            else:
                return "🟡"
        
        # 显示记录
        for record in history_data:
            with st.container():
                col1, col2, col3, col4 = st.columns([2, 1, 3, 1])
                
                with col1:
                    st.text(record["时间"])
                
                with col2:
                    st.text(record["类型"])
                
                with col3:
                    st.text(record["内容"][:40] + "..." if len(record["内容"]) > 40 else record["内容"])
                
                with col4:
                    st.text(f"{get_status_color(record['状态'])} {record['状态']}")
                
                st.markdown("---")
    
    def send_text_message(self, content: str, at_all: bool = False, at_mobiles: str = "") -> bool:
        """发送文本消息"""
        try:
            message = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }
            
            # 添加@信息
            if at_all or at_mobiles:
                at_info = {}
                if at_all:
                    at_info["isAtAll"] = True
                if at_mobiles:
                    mobiles = [mobile.strip() for mobile in at_mobiles.split(',') if mobile.strip()]
                    at_info["atMobiles"] = mobiles
                
                message["at"] = at_info
            
            return self._send_message(message)
            
        except Exception as e:
            logger.error(f"发送文本消息失败: {e}")
            return False
    
    def send_markdown_message(self, title: str, content: str) -> bool:
        """发送Markdown消息"""
        try:
            message = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": content
                }
            }
            
            return self._send_message(message)
            
        except Exception as e:
            logger.error(f"发送Markdown消息失败: {e}")
            return False
    
    def send_link_message(self, title: str, text: str, message_url: str, pic_url: str = "") -> bool:
        """发送链接消息"""
        try:
            message = {
                "msgtype": "link",
                "link": {
                    "title": title,
                    "text": text,
                    "messageUrl": message_url
                }
            }
            
            if pic_url:
                message["link"]["picUrl"] = pic_url
            
            return self._send_message(message)
            
        except Exception as e:
            logger.error(f"发送链接消息失败: {e}")
            return False
    
    def send_tweet_notification(self, tweet: Dict[str, Any]) -> bool:
        """发送推文通知"""
        try:
            # 构建推文消息
            author = tweet.get('author_username', 'unknown')
            text = tweet.get('text', '')
            created_at = tweet.get('created_at', '')
            like_count = tweet.get('like_count', 0)
            retweet_count = tweet.get('retweet_count', 0)
            reply_count = tweet.get('reply_count', 0)
            monitor_type = tweet.get('monitor_type', 'unknown')
            target = tweet.get('target', 'unknown')
            
            # 格式化时间
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                formatted_time = created_at
            
            # 构建Markdown消息
            title = f"🐦 Twitter监测通知"
            
            content = f"""
### 🐦 发现新推文

**监测类型**: {'📱 账号监测' if monitor_type == 'account' else '🔍 关键词监测'}  
**监测目标**: {target}  
**作者**: @{author}  
**发布时间**: {formatted_time}  

**推文内容**:  
{text}

**互动数据**:  
❤️ 点赞: {like_count} | 🔄 转发: {retweet_count} | 💬 回复: {reply_count}

---
*Twitter监测系统自动推送*
"""
            
            return self.send_markdown_message(title, content)
            
        except Exception as e:
            logger.error(f"发送推文通知失败: {e}")
            return False
    
    def _send_message(self, message: Dict[str, Any]) -> bool:
        """发送消息到钉钉"""
        try:
            if not self.webhook_url:
                logger.error("Webhook URL未配置")
                return False
            
            # 生成签名
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            
            # 构建请求URL
            url = f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"
            
            # 发送请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(url, json=message, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info("钉钉消息发送成功")
                    return True
                else:
                    logger.error(f"钉钉消息发送失败: {result.get('errmsg')}")
                    return False
            else:
                logger.error(f"钉钉API请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送钉钉消息异常: {e}")
            return False
    
    def _generate_sign(self, timestamp: str) -> str:
        """生成钉钉签名"""
        try:
            if not self.secret:
                return ""
            
            string_to_sign = f"{timestamp}\n{self.secret}"
            hmac_code = hmac.new(
                self.secret.encode('utf-8'),
                string_to_sign.encode('utf-8'),
                digestmod=hashlib.sha256
            ).digest()
            
            sign = base64.b64encode(hmac_code).decode('utf-8')
            return sign
            
        except Exception as e:
            logger.error(f"生成钉钉签名失败: {e}")
            return ""
    
    def test_connection(self):
        """测试钉钉连接"""
        try:
            if not self.webhook_url or not self.secret:
                st.error("❌ 请先配置Webhook地址和密钥")
                return
            
            # 发送测试消息
            test_message = "🧪 这是一条来自Twitter监测系统的测试消息"
            success = self.send_text_message(test_message)
            
            if success:
                st.success("✅ 钉钉连接测试成功")
            else:
                st.error("❌ 钉钉连接测试失败，请检查配置")
                
        except Exception as e:
            st.error(f"❌ 连接测试异常: {e}")
    
    def render_tweet_template(self):
        """渲染推文模板"""
        st.text_area(
            "新推文通知模板",
            value="""🐦 发现新推文

**监测目标**: {target}
**作者**: @{author}
**时间**: {time}

**内容**: {text}

**互动**: ❤️{likes} 🔄{retweets} 💬{replies}""",
            height=150,
            disabled=True
        )
    
    def render_keyword_template(self):
        """渲染关键词模板"""
        st.text_area(
            "关键词命中模板",
            value="""🔍 关键词命中通知

**关键词**: {keyword}
**命中推文**: {tweet_count}条

**最新推文**:
@{author}: {text}""",
            height=150,
            disabled=True
        )
    
    def render_summary_template(self):
        """渲染摘要模板"""
        st.text_area(
            "监测摘要模板",
            value="""📊 监测摘要报告

**时间范围**: {time_range}
**监测账号**: {account_count}个
**监测关键词**: {keyword_count}个
**新推文**: {tweet_count}条
**成功率**: {success_rate}%""",
            height=150,
            disabled=True
        )
    
    def render_error_template(self):
        """渲染错误模板"""
        st.text_area(
            "错误通知模板",
            value="""⚠️ 监测错误通知

**错误时间**: {time}
**错误类型**: {error_type}
**错误详情**: {error_message}

请检查系统状态和网络连接。""",
            height=150,
            disabled=True
        )
