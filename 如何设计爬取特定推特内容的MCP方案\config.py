"""
配置管理工具
处理应用程序配置文件的加载和管理
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or self._find_config_file()
        self.config_data = {}
        self._load_config()
    
    def _find_config_file(self) -> str:
        """查找配置文件"""
        possible_paths = [
            "config.yaml",
            "config.yml", 
            "config.json",
            "../config/config.yaml",
            "../config/config.yml",
            "../config/config.json",
            os.path.expanduser("~/.twitter-mcp/config.yaml"),
            "/etc/twitter-mcp/config.yaml"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"找到配置文件: {path}")
                return path
        
        # 如果没有找到配置文件，创建默认配置
        default_path = "config.yaml"
        self._create_default_config(default_path)
        return default_path
    
    def _create_default_config(self, path: str):
        """创建默认配置文件"""
        default_config = {
            "twitter": {
                "username": "",
                "password": "",
                "cookies_path": "cookies.json"
            },
            "scraper": {
                "default_count": 20,
                "max_count": 100,
                "default_max_age_days": 30,
                "request_delay": 2,
                "daily_request_limit": 1000
            },
            "anti_block": {
                "requests_per_minute": 30,
                "burst_limit": 5,
                "proxies": [],
                "user_agents": [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
                ]
            },
            "llm": {
                "provider": "openai",
                "openai": {
                    "api_key": "",
                    "model": "gpt-3.5-turbo",
                    "base_url": "https://api.openai.com/v1"
                },
                "anthropic": {
                    "api_key": "",
                    "model": "claude-3-sonnet-20240229"
                },
                "ollama": {
                    "base_url": "http://localhost:11434",
                    "model": "llama2"
                }
            },
            "mcp": {
                "server_name": "Twitter MCP Scraper",
                "version": "1.0.0",
                "protocol_version": "2024-11-05"
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "twitter-mcp.log",
                "max_size": "10MB",
                "backup_count": 5
            }
        }
        
        try:
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            logger.info(f"已创建默认配置文件: {path}")
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {e}")
    
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            logger.warning(f"配置文件不存在: {self.config_path}")
            return
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.endswith('.json'):
                    self.config_data = json.load(f)
                else:
                    self.config_data = yaml.safe_load(f)
            
            logger.info(f"已加载配置文件: {self.config_path}")
            
            # 从环境变量覆盖配置
            self._load_env_overrides()
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.config_data = {}
    
    def _load_env_overrides(self):
        """从环境变量加载配置覆盖"""
        env_mappings = {
            'TWITTER_USERNAME': ['twitter', 'username'],
            'TWITTER_PASSWORD': ['twitter', 'password'],
            'TWITTER_COOKIES_PATH': ['twitter', 'cookies_path'],
            'OPENAI_API_KEY': ['llm', 'openai', 'api_key'],
            'ANTHROPIC_API_KEY': ['llm', 'anthropic', 'api_key'],
            'OLLAMA_BASE_URL': ['llm', 'ollama', 'base_url'],
            'LOG_LEVEL': ['logging', 'level'],
            'PROXY_LIST': ['anti_block', 'proxies'],
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                # 特殊处理代理列表
                if env_var == 'PROXY_LIST':
                    env_value = env_value.split(',')
                
                # 设置嵌套配置
                current = self.config_data
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                current[config_path[-1]] = env_value
                
                logger.debug(f"从环境变量覆盖配置: {env_var} -> {'.'.join(config_path)}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key_path.split('.')
        current = self.config_data
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """设置配置值"""
        keys = key_path.split('.')
        current = self.config_data
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def save(self, path: str = None):
        """保存配置到文件"""
        save_path = path or self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                if save_path.endswith('.json'):
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)
                else:
                    yaml.dump(self.config_data, f, default_flow_style=False,
                             allow_unicode=True, indent=2)
            
            logger.info(f"配置已保存到: {save_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    @property
    def twitter(self) -> Dict[str, Any]:
        """Twitter配置"""
        return self.config_data.get('twitter', {})
    
    @property
    def scraper(self) -> Dict[str, Any]:
        """爬虫配置"""
        return self.config_data.get('scraper', {})
    
    @property
    def anti_block(self) -> Dict[str, Any]:
        """反屏蔽配置"""
        return self.config_data.get('anti_block', {})
    
    @property
    def llm(self) -> Dict[str, Any]:
        """大模型配置"""
        return self.config_data.get('llm', {})
    
    @property
    def mcp(self) -> Dict[str, Any]:
        """MCP配置"""
        return self.config_data.get('mcp', {})
    
    @property
    def logging_config(self) -> Dict[str, Any]:
        """日志配置"""
        return self.config_data.get('logging', {})
    
    def validate(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 检查必需的Twitter配置
        if not self.twitter.get('username') and not os.path.exists(self.twitter.get('cookies_path', '')):
            errors.append("需要提供Twitter用户名或有效的cookies文件")
        
        # 检查LLM配置
        llm_provider = self.llm.get('provider', 'openai')
        if llm_provider == 'openai' and not self.llm.get('openai', {}).get('api_key'):
            errors.append("使用OpenAI时需要提供API密钥")
        elif llm_provider == 'anthropic' and not self.llm.get('anthropic', {}).get('api_key'):
            errors.append("使用Anthropic时需要提供API密钥")
        
        # 检查代理格式
        proxies = self.anti_block.get('proxies', [])
        for proxy in proxies:
            if not isinstance(proxy, str) or ':' not in proxy:
                errors.append(f"代理格式错误: {proxy}")
        
        return errors
    
    def __str__(self) -> str:
        """配置的字符串表示"""
        # 隐藏敏感信息
        safe_config = self._mask_sensitive_data(self.config_data.copy())
        return json.dumps(safe_config, indent=2, ensure_ascii=False)
    
    def _mask_sensitive_data(self, data: Any) -> Any:
        """隐藏敏感数据"""
        if isinstance(data, dict):
            masked = {}
            for key, value in data.items():
                if key.lower() in ['password', 'api_key', 'token', 'secret']:
                    masked[key] = '***' if value else ''
                else:
                    masked[key] = self._mask_sensitive_data(value)
            return masked
        elif isinstance(data, list):
            return [self._mask_sensitive_data(item) for item in data]
        else:
            return data


# 测试代码
if __name__ == "__main__":
    # 测试配置管理
    config = Config()
    
    print("配置内容:")
    print(config)
    
    print("\n配置验证:")
    errors = config.validate()
    if errors:
        for error in errors:
            print(f"- {error}")
    else:
        print("配置验证通过")
    
    print(f"\nTwitter用户名: {config.get('twitter.username', '未设置')}")
    print(f"LLM提供商: {config.get('llm.provider', 'openai')}")
    print(f"日志级别: {config.get('logging.level', 'INFO')}")

