"""
结果展示模块
提供监测结果的详细展示和分析
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
from typing import List, Dict, Any


class ResultsViewer:
    """结果展示器"""
    
    def __init__(self):
        pass
    
    def render(self, results: List[Dict[str, Any]]):
        """渲染结果展示界面"""
        st.markdown('<h1 class="main-header">📊 监测结果展示</h1>', unsafe_allow_html=True)
        
        if not results:
            st.info("暂无监测结果数据")
            return
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📝 推文列表", "📈 数据统计", "🔍 搜索过滤", "📋 详细信息"])
        
        with tab1:
            self.render_tweet_list(results)
        
        with tab2:
            self.render_statistics(results)
        
        with tab3:
            self.render_search_filter(results)
        
        with tab4:
            self.render_detailed_info(results)
    
    def render_tweet_list(self, results: List[Dict[str, Any]]):
        """渲染推文列表"""
        st.markdown("### 📝 最新推文列表")
        
        # 提取所有推文
        all_tweets = []
        for result in results:
            tweets = result.get('tweets', [])
            for tweet in tweets:
                tweet_data = {
                    'monitor_type': result.get('monitor_type', 'unknown'),
                    'target': result.get('target', 'unknown'),
                    'timestamp': result.get('timestamp', ''),
                    **tweet
                }
                all_tweets.append(tweet_data)
        
        if not all_tweets:
            st.info("暂无推文数据")
            return
        
        # 排序选项
        col1, col2, col3 = st.columns([2, 2, 1])
        
        with col1:
            sort_by = st.selectbox(
                "排序方式",
                ["时间（最新）", "时间（最旧）", "点赞数", "转发数", "回复数"],
                index=0
            )
        
        with col2:
            filter_type = st.selectbox(
                "筛选类型",
                ["全部", "账号监测", "关键词监测"],
                index=0
            )
        
        with col3:
            page_size = st.selectbox(
                "每页显示",
                [10, 20, 50, 100],
                index=1
            )
        
        # 应用筛选
        filtered_tweets = self.filter_tweets(all_tweets, filter_type)
        
        # 应用排序
        sorted_tweets = self.sort_tweets(filtered_tweets, sort_by)
        
        # 分页
        total_tweets = len(sorted_tweets)
        total_pages = (total_tweets - 1) // page_size + 1 if total_tweets > 0 else 1
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            current_page = st.number_input(
                f"页码 (共 {total_pages} 页)",
                min_value=1,
                max_value=total_pages,
                value=1
            )
        
        # 显示当前页的推文
        start_idx = (current_page - 1) * page_size
        end_idx = start_idx + page_size
        page_tweets = sorted_tweets[start_idx:end_idx]
        
        # 显示推文卡片
        for i, tweet in enumerate(page_tweets):
            self.render_tweet_card(tweet, start_idx + i + 1)
        
        # 显示统计信息
        st.markdown(f"**总计**: {total_tweets} 条推文 | **当前页**: {len(page_tweets)} 条")
    
    def render_tweet_card(self, tweet: Dict[str, Any], index: int):
        """渲染单个推文卡片"""
        with st.container():
            # 推文头部信息
            col1, col2, col3 = st.columns([3, 1, 1])
            
            with col1:
                st.markdown(f"**#{index} @{tweet.get('author_username', 'unknown')}** ({tweet.get('author_display_name', 'Unknown')})")
            
            with col2:
                monitor_type = tweet.get('monitor_type', 'unknown')
                if monitor_type == 'account':
                    st.markdown("📱 账号监测")
                elif monitor_type == 'keyword':
                    st.markdown("🔍 关键词监测")
                else:
                    st.markdown(f"📊 {monitor_type}")
            
            with col3:
                created_at = tweet.get('created_at', '')
                if created_at:
                    try:
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        st.markdown(f"🕒 {dt.strftime('%m-%d %H:%M')}")
                    except:
                        st.markdown(f"🕒 {created_at[:16]}")
            
            # 推文内容
            text = tweet.get('text', '')
            if len(text) > 300:
                with st.expander(f"📄 {text[:100]}..."):
                    st.markdown(text)
            else:
                st.markdown(f"📄 {text}")
            
            # 互动数据和操作
            col1, col2, col3, col4, col5 = st.columns([1, 1, 1, 1, 2])
            
            with col1:
                st.metric("❤️", tweet.get('like_count', 0))
            
            with col2:
                st.metric("🔄", tweet.get('retweet_count', 0))
            
            with col3:
                st.metric("💬", tweet.get('reply_count', 0))
            
            with col4:
                if st.button("🔔", key=f"notify_{index}", help="推送到钉钉"):
                    self.push_to_dingtalk(tweet)
            
            with col5:
                # 标签和提及
                hashtags = tweet.get('hashtags', [])
                mentions = tweet.get('mentions', [])
                
                if hashtags:
                    st.markdown(f"🏷️ {' '.join(['#' + tag for tag in hashtags[:3]])}")
                if mentions:
                    st.markdown(f"👥 {' '.join(['@' + mention for mention in mentions[:3]])}")
            
            st.markdown("---")
    
    def render_statistics(self, results: List[Dict[str, Any]]):
        """渲染数据统计"""
        st.markdown("### 📈 数据统计分析")
        
        # 基本统计
        total_results = len(results)
        total_tweets = sum(len(result.get('tweets', [])) for result in results)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("监测次数", total_results)
        
        with col2:
            st.metric("总推文数", total_tweets)
        
        with col3:
            avg_tweets = total_tweets / total_results if total_results > 0 else 0
            st.metric("平均推文/次", f"{avg_tweets:.1f}")
        
        with col4:
            success_count = sum(1 for result in results if result.get('success', False))
            success_rate = success_count / total_results * 100 if total_results > 0 else 0
            st.metric("成功率", f"{success_rate:.1f}%")
        
        # 时间趋势图
        self.render_time_trend_chart(results)
        
        # 监测类型分布
        self.render_monitor_type_chart(results)
        
        # 互动数据分析
        self.render_engagement_analysis(results)
    
    def render_time_trend_chart(self, results: List[Dict[str, Any]]):
        """渲染时间趋势图"""
        st.markdown("#### 📊 监测趋势")
        
        # 处理时间数据
        time_data = []
        for result in results:
            timestamp = result.get('timestamp', '')
            tweet_count = len(result.get('tweets', []))
            
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_data.append({
                    'time': dt,
                    'tweets': tweet_count,
                    'type': result.get('monitor_type', 'unknown'),
                    'target': result.get('target', 'unknown')
                })
            except:
                continue
        
        if not time_data:
            st.info("暂无时间数据")
            return
        
        df = pd.DataFrame(time_data)
        
        # 创建时间趋势图
        fig = px.line(
            df, 
            x='time', 
            y='tweets',
            color='type',
            title='监测推文数量趋势',
            labels={'time': '时间', 'tweets': '推文数量', 'type': '监测类型'}
        )
        
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    def render_monitor_type_chart(self, results: List[Dict[str, Any]]):
        """渲染监测类型分布图"""
        st.markdown("#### 📊 监测类型分布")
        
        # 统计监测类型
        type_stats = {}
        target_stats = {}
        
        for result in results:
            monitor_type = result.get('monitor_type', 'unknown')
            target = result.get('target', 'unknown')
            tweet_count = len(result.get('tweets', []))
            
            type_stats[monitor_type] = type_stats.get(monitor_type, 0) + tweet_count
            target_stats[target] = target_stats.get(target, 0) + tweet_count
        
        col1, col2 = st.columns(2)
        
        with col1:
            if type_stats:
                fig = px.pie(
                    values=list(type_stats.values()),
                    names=list(type_stats.keys()),
                    title='按监测类型分布'
                )
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            if target_stats:
                # 只显示前10个目标
                sorted_targets = sorted(target_stats.items(), key=lambda x: x[1], reverse=True)[:10]
                
                fig = px.bar(
                    x=[item[1] for item in sorted_targets],
                    y=[item[0] for item in sorted_targets],
                    orientation='h',
                    title='热门监测目标 (Top 10)',
                    labels={'x': '推文数量', 'y': '监测目标'}
                )
                st.plotly_chart(fig, use_container_width=True)
    
    def render_engagement_analysis(self, results: List[Dict[str, Any]]):
        """渲染互动数据分析"""
        st.markdown("#### 📊 互动数据分析")
        
        # 收集所有推文的互动数据
        engagement_data = []
        for result in results:
            tweets = result.get('tweets', [])
            for tweet in tweets:
                engagement_data.append({
                    'like_count': tweet.get('like_count', 0),
                    'retweet_count': tweet.get('retweet_count', 0),
                    'reply_count': tweet.get('reply_count', 0),
                    'author': tweet.get('author_username', 'unknown')
                })
        
        if not engagement_data:
            st.info("暂无互动数据")
            return
        
        df = pd.DataFrame(engagement_data)
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 互动数据分布
            fig = go.Figure()
            
            fig.add_trace(go.Box(y=df['like_count'], name='点赞数'))
            fig.add_trace(go.Box(y=df['retweet_count'], name='转发数'))
            fig.add_trace(go.Box(y=df['reply_count'], name='回复数'))
            
            fig.update_layout(
                title='互动数据分布',
                yaxis_title='数量',
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 平均互动数据
            avg_stats = {
                '平均点赞': df['like_count'].mean(),
                '平均转发': df['retweet_count'].mean(),
                '平均回复': df['reply_count'].mean()
            }
            
            fig = px.bar(
                x=list(avg_stats.keys()),
                y=list(avg_stats.values()),
                title='平均互动数据'
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    def render_search_filter(self, results: List[Dict[str, Any]]):
        """渲染搜索过滤"""
        st.markdown("### 🔍 搜索和过滤")
        
        # 搜索框
        search_text = st.text_input("🔍 搜索推文内容", placeholder="输入关键词搜索推文...")
        
        # 过滤选项
        col1, col2, col3 = st.columns(3)
        
        with col1:
            date_filter = st.date_input("📅 日期筛选", value=None)
        
        with col2:
            author_filter = st.text_input("👤 作者筛选", placeholder="输入用户名...")
        
        with col3:
            min_likes = st.number_input("❤️ 最小点赞数", min_value=0, value=0)
        
        # 应用搜索和过滤
        filtered_results = self.apply_search_filter(
            results, search_text, date_filter, author_filter, min_likes
        )
        
        st.markdown(f"**搜索结果**: 找到 {len(filtered_results)} 条相关推文")
        
        # 显示搜索结果
        if filtered_results:
            for i, tweet in enumerate(filtered_results[:20]):  # 限制显示20条
                self.render_tweet_card(tweet, i + 1)
        else:
            st.info("未找到符合条件的推文")
    
    def render_detailed_info(self, results: List[Dict[str, Any]]):
        """渲染详细信息"""
        st.markdown("### 📋 详细监测信息")
        
        # 选择查看的结果
        if results:
            result_options = [
                f"{result.get('monitor_type', 'unknown')} - {result.get('target', 'unknown')} ({result.get('timestamp', '')[:16]})"
                for result in results
            ]
            
            selected_index = st.selectbox(
                "选择监测结果",
                range(len(result_options)),
                format_func=lambda x: result_options[x]
            )
            
            selected_result = results[selected_index]
            
            # 显示详细信息
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("#### 📊 监测信息")
                st.json({
                    'monitor_type': selected_result.get('monitor_type'),
                    'target': selected_result.get('target'),
                    'timestamp': selected_result.get('timestamp'),
                    'success': selected_result.get('success'),
                    'tweet_count': len(selected_result.get('tweets', [])),
                    'error_message': selected_result.get('error_message')
                })
            
            with col2:
                st.markdown("#### 📝 推文详情")
                tweets = selected_result.get('tweets', [])
                if tweets:
                    selected_tweet_index = st.selectbox(
                        "选择推文",
                        range(len(tweets)),
                        format_func=lambda x: f"推文 {x+1}: {tweets[x].get('text', '')[:50]}..."
                    )
                    
                    selected_tweet = tweets[selected_tweet_index]
                    st.json(selected_tweet)
                else:
                    st.info("该监测结果无推文数据")
    
    def filter_tweets(self, tweets: List[Dict[str, Any]], filter_type: str) -> List[Dict[str, Any]]:
        """过滤推文"""
        if filter_type == "全部":
            return tweets
        elif filter_type == "账号监测":
            return [t for t in tweets if t.get('monitor_type') == 'account']
        elif filter_type == "关键词监测":
            return [t for t in tweets if t.get('monitor_type') == 'keyword']
        else:
            return tweets
    
    def sort_tweets(self, tweets: List[Dict[str, Any]], sort_by: str) -> List[Dict[str, Any]]:
        """排序推文"""
        if sort_by == "时间（最新）":
            return sorted(tweets, key=lambda x: x.get('created_at', ''), reverse=True)
        elif sort_by == "时间（最旧）":
            return sorted(tweets, key=lambda x: x.get('created_at', ''))
        elif sort_by == "点赞数":
            return sorted(tweets, key=lambda x: x.get('like_count', 0), reverse=True)
        elif sort_by == "转发数":
            return sorted(tweets, key=lambda x: x.get('retweet_count', 0), reverse=True)
        elif sort_by == "回复数":
            return sorted(tweets, key=lambda x: x.get('reply_count', 0), reverse=True)
        else:
            return tweets
    
    def apply_search_filter(self, results: List[Dict[str, Any]], search_text: str, 
                          date_filter, author_filter: str, min_likes: int) -> List[Dict[str, Any]]:
        """应用搜索和过滤"""
        filtered_tweets = []
        
        for result in results:
            tweets = result.get('tweets', [])
            for tweet in tweets:
                # 文本搜索
                if search_text and search_text.lower() not in tweet.get('text', '').lower():
                    continue
                
                # 作者过滤
                if author_filter and author_filter.lower() not in tweet.get('author_username', '').lower():
                    continue
                
                # 点赞数过滤
                if tweet.get('like_count', 0) < min_likes:
                    continue
                
                # 日期过滤
                if date_filter:
                    try:
                        tweet_date = datetime.fromisoformat(tweet.get('created_at', '').replace('Z', '+00:00')).date()
                        if tweet_date != date_filter:
                            continue
                    except:
                        continue
                
                # 添加监测信息
                tweet_data = {
                    'monitor_type': result.get('monitor_type'),
                    'target': result.get('target'),
                    'timestamp': result.get('timestamp'),
                    **tweet
                }
                filtered_tweets.append(tweet_data)
        
        return filtered_tweets
    
    def push_to_dingtalk(self, tweet: Dict[str, Any]):
        """推送推文到钉钉"""
        try:
            from .dingtalk_bot import DingTalkBot
            bot = DingTalkBot()
            
            success = bot.send_tweet_notification(tweet)
            if success:
                st.success("✅ 推文已推送到钉钉")
            else:
                st.error("❌ 推送失败，请检查钉钉配置")
                
        except Exception as e:
            st.error(f"❌ 推送失败: {e}")
