# Twitter MCP Scraper API 文档

## 概述

Twitter MCP Scraper 提供基于 Model Context Protocol (MCP) 的标准化API接口，支持推文爬取和智能分析功能。

## MCP协议基础

### 消息格式

所有MCP消息都遵循JSON-RPC 2.0规范：

```json
{
  "jsonrpc": "2.0",
  "id": "unique-request-id",
  "method": "method-name",
  "params": {
    // 方法参数
  }
}
```

### 响应格式

成功响应：
```json
{
  "jsonrpc": "2.0", 
  "id": "unique-request-id",
  "result": {
    // 结果数据
  }
}
```

错误响应：
```json
{
  "jsonrpc": "2.0",
  "id": "unique-request-id", 
  "error": {
    "code": -32603,
    "message": "错误描述"
  }
}
```

## 核心方法

### 1. initialize - 初始化连接

初始化MCP连接，必须在使用其他方法前调用。

**请求**：
```json
{
  "jsonrpc": "2.0",
  "id": "init-1",
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {},
    "clientInfo": {
      "name": "client-name",
      "version": "1.0.0"
    }
  }
}
```

**响应**：
```json
{
  "jsonrpc": "2.0",
  "id": "init-1", 
  "result": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "tools": {"listChanged": true},
      "resources": {"listChanged": true}
    },
    "serverInfo": {
      "name": "Twitter MCP Scraper",
      "version": "1.0.0",
      "description": "Twitter爬虫MCP服务器"
    }
  }
}
```

### 2. tools/list - 获取工具列表

获取所有可用的工具及其描述。

**请求**：
```json
{
  "jsonrpc": "2.0",
  "id": "tools-1",
  "method": "tools/list"
}
```

**响应**：
```json
{
  "jsonrpc": "2.0",
  "id": "tools-1",
  "result": {
    "tools": [
      {
        "name": "scrape_user_tweets",
        "description": "爬取指定用户的推文内容",
        "inputSchema": {
          "type": "object",
          "properties": {
            "username": {
              "type": "string",
              "description": "Twitter用户名（不包含@符号）"
            },
            "count": {
              "type": "integer", 
              "description": "爬取推文数量",
              "default": 20,
              "minimum": 1,
              "maximum": 100
            }
          },
          "required": ["username"]
        }
      }
    ]
  }
}
```

### 3. tools/call - 调用工具

调用指定的工具执行操作。

**请求格式**：
```json
{
  "jsonrpc": "2.0",
  "id": "call-1",
  "method": "tools/call",
  "params": {
    "name": "tool-name",
    "arguments": {
      // 工具参数
    }
  }
}
```

**响应格式**：
```json
{
  "jsonrpc": "2.0",
  "id": "call-1",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "JSON格式的结果数据"
      }
    ]
  }
}
```

## 工具详细说明

### scrape_user_tweets - 爬取用户推文

爬取指定Twitter用户的推文内容。

**参数**：
- `username` (string, 必需): Twitter用户名，不包含@符号
- `count` (integer, 可选): 爬取推文数量，默认20，范围1-100
- `include_replies` (boolean, 可选): 是否包含回复推文，默认false
- `max_age_days` (integer, 可选): 最大时间范围（天），默认30

**示例请求**：
```json
{
  "jsonrpc": "2.0",
  "id": "scrape-1",
  "method": "tools/call",
  "params": {
    "name": "scrape_user_tweets",
    "arguments": {
      "username": "elonmusk",
      "count": 10,
      "include_replies": false,
      "max_age_days": 7
    }
  }
}
```

**示例响应**：
```json
{
  "jsonrpc": "2.0",
  "id": "scrape-1",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\"success\": true, \"data\": {\"username\": \"elonmusk\", \"tweet_count\": 10, \"tweets\": [...]}}"
      }
    ]
  }
}
```

**返回数据结构**：
```json
{
  "success": true,
  "data": {
    "username": "elonmusk",
    "tweet_count": 10,
    "tweets": [
      {
        "id": "1234567890",
        "text": "推文内容",
        "author": {
          "username": "elonmusk",
          "display_name": "Elon Musk",
          "followers_count": 50000000,
          "verified": true
        },
        "created_at": "2024-01-01T12:00:00Z",
        "metrics": {
          "retweet_count": 1000,
          "like_count": 5000,
          "reply_count": 200,
          "quote_count": 100
        },
        "content": {
          "hashtags": ["#Tesla", "#SpaceX"],
          "mentions": ["@Tesla"],
          "urls": ["https://example.com"],
          "media": [
            {
              "type": "photo",
              "url": "https://pbs.twimg.com/media/..."
            }
          ]
        },
        "metadata": {
          "language": "en",
          "is_retweet": false,
          "is_reply": false
        }
      }
    ],
    "scraped_at": "2024-01-01T12:00:00Z",
    "parameters": {
      "count": 10,
      "include_replies": false,
      "max_age_days": 7
    }
  }
}
```

### scrape_keyword_tweets - 搜索关键词推文

根据关键词搜索并爬取相关推文。

**参数**：
- `keyword` (string, 必需): 搜索关键词
- `count` (integer, 可选): 爬取推文数量，默认20，范围1-100
- `language` (string, 可选): 语言过滤，如"zh"、"en"、"ja"
- `date_range` (string, 可选): 时间范围，如"7d"、"30d"、"1y"

**示例请求**：
```json
{
  "jsonrpc": "2.0",
  "id": "search-1",
  "method": "tools/call",
  "params": {
    "name": "scrape_keyword_tweets",
    "arguments": {
      "keyword": "人工智能",
      "count": 20,
      "language": "zh",
      "date_range": "7d"
    }
  }
}
```

### analyze_tweets - 分析推文内容

使用大模型分析推文内容，提供情感分析、摘要和主题提取。

**参数**：
- `tweets` (array, 必需): 推文数据数组
- `analysis_type` (string, 可选): 分析类型，可选值：
  - `"sentiment"`: 仅情感分析
  - `"summary"`: 仅内容摘要
  - `"topics"`: 仅主题提取
  - `"all"`: 全部分析（默认）
- `language` (string, 可选): 分析语言，默认"zh"

**示例请求**：
```json
{
  "jsonrpc": "2.0",
  "id": "analyze-1",
  "method": "tools/call",
  "params": {
    "name": "analyze_tweets",
    "arguments": {
      "tweets": [
        {"text": "今天天气真好！"},
        {"text": "对新政策感到担忧"}
      ],
      "analysis_type": "sentiment",
      "language": "zh"
    }
  }
}
```

**分析结果结构**：
```json
{
  "success": true,
  "data": {
    "tweet_count": 2,
    "analysis_type": "sentiment",
    "language": "zh",
    "analyzed_at": "2024-01-01T12:00:00Z",
    "results": {
      "sentiment": {
        "overall_sentiment": "mixed",
        "sentiment_score": 0.6,
        "positive_ratio": 0.5,
        "negative_ratio": 0.3,
        "neutral_ratio": 0.2,
        "confidence": 0.85,
        "details": [
          {
            "text": "今天天气真好！",
            "sentiment": "positive",
            "score": 0.9
          }
        ]
      },
      "summary": {
        "summary": "推文内容主要涉及天气和政策话题",
        "main_topics": ["天气", "政策"],
        "key_points": ["积极情绪", "担忧情绪"],
        "tone": "混合",
        "word_count": 20,
        "summary_ratio": 0.5
      },
      "topics": {
        "topics": [
          {
            "topic": "天气",
            "relevance": 0.8,
            "frequency": 1
          }
        ],
        "categories": ["生活", "政治"],
        "hashtags": [],
        "trending_topics": ["天气"],
        "topic_distribution": {
          "生活": 0.5,
          "政治": 0.5
        }
      }
    },
    "input_stats": {
      "tweet_count": 2,
      "text_count": 2,
      "total_characters": 20
    }
  }
}
```

### get_scraper_status - 获取系统状态

获取爬虫系统的运行状态和统计信息。

**参数**：无

**示例请求**：
```json
{
  "jsonrpc": "2.0",
  "id": "status-1",
  "method": "tools/call",
  "params": {
    "name": "get_scraper_status",
    "arguments": {}
  }
}
```

**状态信息结构**：
```json
{
  "success": true,
  "data": {
    "app_status": {
      "is_running": true,
      "start_time": "2024-01-01T10:00:00Z",
      "uptime_seconds": 3600
    },
    "scraper_status": {
      "is_initialized": true,
      "is_logged_in": true,
      "request_count": 150,
      "daily_limit": 1000,
      "last_request_time": 1704110400,
      "session_active": true
    },
    "anti_block_status": {
      "proxy_status": {
        "total_proxies": 3,
        "failed_proxies": 0,
        "current_index": 1,
        "last_rotation_time": **********
      },
      "session_count": 2,
      "stats": {
        "total_requests": 150,
        "blocked_requests": 2,
        "proxy_rotations": 5,
        "last_block_time": **********
      },
      "rate_limiter": {
        "requests_per_minute": 30,
        "current_requests": 5,
        "burst_count": 2
      }
    },
    "llm_status": {
      "available_providers": ["openai", "ollama"],
      "default_provider": "openai",
      "provider_status": {
        "openai": {
          "name": "OpenAIProvider",
          "config_keys": ["api_key", "model", "base_url"]
        }
      }
    },
    "mcp_server": {
      "initialized": true,
      "registered_tools": 4,
      "registered_resources": 0
    }
  }
}
```

## 错误处理

### 错误代码

| 代码 | 含义 | 描述 |
|------|------|------|
| -32700 | Parse error | JSON解析错误 |
| -32600 | Invalid Request | 无效请求 |
| -32601 | Method not found | 方法不存在 |
| -32602 | Invalid params | 参数无效 |
| -32603 | Internal error | 内部错误 |

### 常见错误示例

**参数错误**：
```json
{
  "jsonrpc": "2.0",
  "id": "error-1",
  "error": {
    "code": -32602,
    "message": "用户名不能为空"
  }
}
```

**工具不存在**：
```json
{
  "jsonrpc": "2.0",
  "id": "error-2", 
  "error": {
    "code": -32601,
    "message": "未知工具: invalid_tool_name"
  }
}
```

**内部错误**：
```json
{
  "jsonrpc": "2.0",
  "id": "error-3",
  "error": {
    "code": -32603,
    "message": "Twitter登录失败: 用户名或密码错误"
  }
}
```

## 使用示例

### Python客户端示例

```python
import json
import requests

class TwitterMCPClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.request_id = 0
    
    def _make_request(self, method, params=None):
        self.request_id += 1
        payload = {
            "jsonrpc": "2.0",
            "id": str(self.request_id),
            "method": method,
            "params": params or {}
        }
        
        response = self.session.post(
            f"{self.base_url}/mcp",
            json=payload
        )
        return response.json()
    
    def initialize(self):
        return self._make_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "python-client",
                "version": "1.0.0"
            }
        })
    
    def list_tools(self):
        return self._make_request("tools/list")
    
    def scrape_user_tweets(self, username, count=20):
        return self._make_request("tools/call", {
            "name": "scrape_user_tweets",
            "arguments": {
                "username": username,
                "count": count
            }
        })
    
    def analyze_tweets(self, tweets, analysis_type="all"):
        return self._make_request("tools/call", {
            "name": "analyze_tweets", 
            "arguments": {
                "tweets": tweets,
                "analysis_type": analysis_type
            }
        })

# 使用示例
client = TwitterMCPClient()

# 初始化
init_result = client.initialize()
print("初始化结果:", init_result)

# 爬取推文
tweets_result = client.scrape_user_tweets("elonmusk", 5)
if tweets_result.get("result"):
    tweets_data = json.loads(tweets_result["result"]["content"][0]["text"])
    print(f"爬取到 {tweets_data['data']['tweet_count']} 条推文")
    
    # 分析推文
    analysis_result = client.analyze_tweets(
        tweets_data["data"]["tweets"], 
        "sentiment"
    )
    print("分析结果:", analysis_result)
```

### JavaScript客户端示例

```javascript
class TwitterMCPClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
        this.requestId = 0;
    }
    
    async makeRequest(method, params = {}) {
        this.requestId++;
        const payload = {
            jsonrpc: '2.0',
            id: this.requestId.toString(),
            method: method,
            params: params
        };
        
        const response = await fetch(`${this.baseUrl}/mcp`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        
        return await response.json();
    }
    
    async initialize() {
        return await this.makeRequest('initialize', {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: {
                name: 'js-client',
                version: '1.0.0'
            }
        });
    }
    
    async scrapeUserTweets(username, count = 20) {
        return await this.makeRequest('tools/call', {
            name: 'scrape_user_tweets',
            arguments: {
                username: username,
                count: count
            }
        });
    }
}

// 使用示例
const client = new TwitterMCPClient();

async function example() {
    // 初始化
    const initResult = await client.initialize();
    console.log('初始化结果:', initResult);
    
    // 爬取推文
    const tweetsResult = await client.scrapeUserTweets('elonmusk', 5);
    console.log('推文结果:', tweetsResult);
}

example();
```

## 性能考虑

### 请求频率限制

- 默认每分钟最多30个请求
- 突发请求限制为5个
- 建议在请求间添加适当延迟

### 批量处理

- 单次最多爬取100条推文
- 大量数据建议分批处理
- 使用异步方式提高效率

### 缓存策略

- 相同请求在短时间内可能返回缓存结果
- 缓存有效期通常为1小时
- 可通过配置调整缓存策略

## 安全注意事项

### 认证和授权

- 目前版本不包含认证机制
- 生产环境建议添加API密钥验证
- 限制访问IP范围

### 数据隐私

- 仅爬取公开推文内容
- 不存储用户敏感信息
- 遵守数据保护法规

### 使用限制

- 遵守Twitter服务条款
- 合理控制爬取频率
- 避免对服务器造成过大负载

---

**文档版本**: 1.0.0  
**最后更新**: 2024年12月  
**作者**: Manus AI

