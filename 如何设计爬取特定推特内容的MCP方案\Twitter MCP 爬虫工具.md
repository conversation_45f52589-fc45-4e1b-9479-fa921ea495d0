# Twitter MCP 爬虫工具

一个基于Model Context Protocol (MCP)的Twitter推文爬虫工具，支持无API爬取、大模型分析和智能反屏蔽策略。

## 项目概述

Twitter MCP爬虫工具是一个专业的推文数据采集和分析系统，采用MCP协议标准，提供统一的接口供AI应用调用。该工具完全基于网页爬虫技术，无需Twitter API，同时集成了多种大模型进行智能内容分析。

### 主要特性

- **无API依赖**: 完全基于网页爬虫，避免API限制和成本
- **MCP协议兼容**: 遵循Model Context Protocol标准，易于集成
- **智能分析**: 集成OpenAI、Anthropic、Ollama等多种大模型
- **反屏蔽策略**: 代理轮换、请求频率控制、User-Agent伪装
- **高可靠性**: 异常处理、自动重试、状态监控
- **易于配置**: YAML配置文件，环境变量支持
- **完整日志**: 详细的操作日志和错误追踪

### 支持的功能

1. **用户推文爬取**: 根据用户名爬取指定用户的推文
2. **关键词搜索**: 根据关键词搜索相关推文
3. **智能分析**: 情感分析、内容摘要、主题提取
4. **状态监控**: 实时监控爬虫状态和性能指标

## 系统要求

- Python 3.8+
- 内存: 最少512MB，推荐1GB+
- 存储: 最少100MB可用空间
- 网络: 稳定的互联网连接

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd twitter-mcp-scraper
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置设置

复制配置模板并编辑：

```bash
cp config.yaml.example config.yaml
```

编辑 `config.yaml` 文件，配置必要的参数：

```yaml
twitter:
  username: "your_twitter_username"
  password: "your_twitter_password"

llm:
  provider: "openai"
  openai:
    api_key: "your_openai_api_key"
```

### 4. 运行测试

```bash
python test_basic.py
```

### 5. 启动服务

```bash
python src/main.py
```

## 详细配置指南

### Twitter配置

```yaml
twitter:
  username: ""          # Twitter用户名
  password: ""          # Twitter密码  
  cookies_path: "cookies.json"  # Cookies保存路径
```

**重要说明**: 
- 建议使用专门的测试账号，避免主账号被封
- 首次登录后会保存cookies，后续可以不提供密码
- 支持两步验证，但需要手动处理

### 爬虫配置

```yaml
scraper:
  default_count: 20     # 默认爬取数量
  max_count: 100        # 最大爬取数量
  default_max_age_days: 30  # 默认时间范围
  request_delay: 2      # 请求间隔（秒）
  daily_request_limit: 1000  # 每日请求限制
```

### 反屏蔽配置

```yaml
anti_block:
  requests_per_minute: 30   # 每分钟请求限制
  burst_limit: 5           # 突发请求限制
  proxies:                 # 代理列表
    - "http://proxy1:8080"
    - "socks5://proxy2:1080"
```

### 大模型配置

#### OpenAI配置
```yaml
llm:
  provider: "openai"
  openai:
    api_key: "sk-..."
    model: "gpt-3.5-turbo"
    base_url: "https://api.openai.com/v1"
    max_tokens: 1000
    temperature: 0.3
```

#### Anthropic配置
```yaml
llm:
  provider: "anthropic"
  anthropic:
    api_key: "sk-ant-..."
    model: "claude-3-sonnet-20240229"
    max_tokens: 1000
```

#### Ollama本地模型配置
```yaml
llm:
  provider: "ollama"
  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"
    timeout: 30
```

## MCP工具使用指南

### 1. 爬取用户推文

```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "tools/call",
  "params": {
    "name": "scrape_user_tweets",
    "arguments": {
      "username": "elonmusk",
      "count": 20,
      "include_replies": false,
      "max_age_days": 7
    }
  }
}
```

### 2. 搜索关键词推文

```json
{
  "jsonrpc": "2.0",
  "id": "2", 
  "method": "tools/call",
  "params": {
    "name": "scrape_keyword_tweets",
    "arguments": {
      "keyword": "人工智能",
      "count": 30,
      "language": "zh",
      "date_range": "7d"
    }
  }
}
```

### 3. 分析推文内容

```json
{
  "jsonrpc": "2.0",
  "id": "3",
  "method": "tools/call", 
  "params": {
    "name": "analyze_tweets",
    "arguments": {
      "tweets": [...],
      "analysis_type": "all",
      "language": "zh"
    }
  }
}
```

### 4. 查询系统状态

```json
{
  "jsonrpc": "2.0",
  "id": "4",
  "method": "tools/call",
  "params": {
    "name": "get_scraper_status",
    "arguments": {}
  }
}
```

## 部署指南

### Docker部署

1. 构建镜像：
```bash
docker build -t twitter-mcp-scraper .
```

2. 运行容器：
```bash
docker run -d \
  -p 8000:8000 \
  -v $(pwd)/config.yaml:/app/config.yaml \
  -v $(pwd)/logs:/app/logs \
  --name twitter-mcp \
  twitter-mcp-scraper
```

### 生产环境部署

1. 使用进程管理器（如systemd）：

创建服务文件 `/etc/systemd/system/twitter-mcp.service`：

```ini
[Unit]
Description=Twitter MCP Scraper
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/twitter-mcp-scraper
ExecStart=/usr/bin/python3 src/main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

2. 启用服务：
```bash
sudo systemctl enable twitter-mcp
sudo systemctl start twitter-mcp
```

### 负载均衡部署

使用Nginx作为反向代理：

```nginx
upstream twitter_mcp {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://twitter_mcp;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 性能优化

### 1. 代理配置

使用高质量代理池提高成功率：

```yaml
anti_block:
  proxies:
    - "http://premium-proxy1:8080"
    - "http://premium-proxy2:8080"
    - "socks5://residential-proxy:1080"
```

### 2. 并发控制

调整请求频率以平衡速度和稳定性：

```yaml
anti_block:
  requests_per_minute: 20  # 降低频率提高稳定性
  burst_limit: 3          # 减少突发请求
```

### 3. 缓存策略

启用结果缓存减少重复请求：

```yaml
scraper:
  enable_cache: true
  cache_ttl: 3600  # 缓存1小时
```

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名密码是否正确
   - 确认账号未被限制
   - 尝试使用cookies文件

2. **爬取失败**
   - 检查网络连接
   - 验证代理设置
   - 降低请求频率

3. **分析错误**
   - 确认API密钥有效
   - 检查模型配置
   - 验证输入数据格式

### 日志分析

查看详细日志：

```bash
tail -f logs/twitter-mcp.log
```

日志级别说明：
- DEBUG: 详细调试信息
- INFO: 一般操作信息
- WARNING: 警告信息
- ERROR: 错误信息

### 性能监控

使用内置状态接口监控系统：

```bash
curl http://localhost:8000/status
```

## 安全注意事项

### 1. 账号安全

- 使用专门的测试账号
- 定期更换密码
- 启用两步验证
- 监控账号状态

### 2. 数据安全

- 加密存储敏感配置
- 定期清理日志文件
- 限制网络访问权限
- 使用HTTPS传输

### 3. 合规使用

- 遵守Twitter服务条款
- 尊重用户隐私
- 合理控制爬取频率
- 仅爬取公开内容

## 法律声明

本工具仅供学习和研究使用。使用者应当：

1. 遵守当地法律法规
2. 遵守Twitter服务条款
3. 尊重用户隐私权
4. 合理使用爬虫功能
5. 承担使用风险和责任

## 贡献指南

欢迎贡献代码和建议：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 支持与反馈

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发送邮件
- 加入讨论群

---

**作者**: Manus AI  
**版本**: 1.0.0  
**更新时间**: 2024年12月

