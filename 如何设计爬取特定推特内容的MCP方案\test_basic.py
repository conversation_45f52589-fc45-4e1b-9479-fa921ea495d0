#!/usr/bin/env python3
"""
Twitter MCP 爬虫测试脚本
测试基本功能和MCP协议兼容性
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from main import TwitterMCPApp
from utils.config import Config
from utils.logger import setup_logger

logger = setup_logger(__name__)


async def test_mcp_protocol():
    """测试MCP协议基本功能"""
    print("=" * 60)
    print("测试MCP协议基本功能")
    print("=" * 60)
    
    try:
        # 创建应用实例
        app = TwitterMCPApp()
        
        # 测试初始化消息
        init_message = {
            "jsonrpc": "2.0",
            "id": "test-init",
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        response = await app.handle_mcp_message(json.dumps(init_message))
        init_result = json.loads(response)
        
        print("✓ MCP初始化测试:")
        print(f"  协议版本: {init_result.get('result', {}).get('protocolVersion')}")
        print(f"  服务器名称: {init_result.get('result', {}).get('serverInfo', {}).get('name')}")
        
        # 测试工具列表
        list_tools_message = {
            "jsonrpc": "2.0",
            "id": "test-tools",
            "method": "tools/list"
        }
        
        response = await app.handle_mcp_message(json.dumps(list_tools_message))
        tools_result = json.loads(response)
        
        tools = tools_result.get('result', {}).get('tools', [])
        print(f"\n✓ 工具列表测试 (共{len(tools)}个工具):")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        return True
        
    except Exception as e:
        print(f"✗ MCP协议测试失败: {e}")
        return False


async def test_configuration():
    """测试配置管理"""
    print("\n" + "=" * 60)
    print("测试配置管理")
    print("=" * 60)
    
    try:
        config = Config()
        
        print("✓ 配置文件加载测试:")
        print(f"  配置文件路径: {config.config_path}")
        print(f"  Twitter用户名: {config.get('twitter.username', '未设置')}")
        print(f"  LLM提供商: {config.get('llm.provider', 'openai')}")
        print(f"  日志级别: {config.get('logging.level', 'INFO')}")
        
        # 验证配置
        errors = config.validate()
        if errors:
            print("\n⚠ 配置验证警告:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("\n✓ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        return False


async def test_scraper_status():
    """测试爬虫状态"""
    print("\n" + "=" * 60)
    print("测试爬虫状态")
    print("=" * 60)
    
    try:
        app = TwitterMCPApp()
        
        # 测试状态查询
        status = await app._handle_get_status()
        
        if status["success"]:
            data = status["data"]
            print("✓ 状态查询测试:")
            print(f"  应用运行状态: {data['app_status']['is_running']}")
            print(f"  爬虫初始化: {data['scraper_status']['is_initialized']}")
            print(f"  爬虫登录状态: {data['scraper_status']['is_logged_in']}")
            print(f"  MCP工具数量: {data['mcp_server']['registered_tools']}")
            print(f"  LLM可用提供商: {data['llm_status']['available_providers']}")
            print(f"  反屏蔽代理数量: {data['anti_block_status']['proxy_status']['total_proxies']}")
        else:
            print(f"✗ 状态查询失败: {status['error']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 爬虫状态测试失败: {e}")
        return False


async def test_llm_integration():
    """测试大模型集成"""
    print("\n" + "=" * 60)
    print("测试大模型集成")
    print("=" * 60)
    
    try:
        app = TwitterMCPApp()
        
        # 测试推文分析（使用模拟数据）
        test_tweets = [
            {
                "id": "1",
                "text": "今天天气真好，心情很棒！#好心情",
                "author_username": "test_user1"
            },
            {
                "id": "2", 
                "text": "对新技术的发展感到担忧，希望能更好地控制风险。",
                "author_username": "test_user2"
            },
            {
                "id": "3",
                "text": "人工智能将改变世界，我们需要做好准备。#AI #未来",
                "author_username": "test_user3"
            }
        ]
        
        # 测试情感分析
        result = await app._handle_analyze_tweets(
            tweets=test_tweets,
            analysis_type="sentiment",
            language="zh"
        )
        
        if result["success"]:
            print("✓ 情感分析测试:")
            sentiment = result["data"]["results"]["sentiment"]
            if sentiment:
                print(f"  整体情感: {sentiment.get('overall_sentiment', 'unknown')}")
                print(f"  置信度: {sentiment.get('confidence', 0):.2f}")
            else:
                print("  情感分析结果为空（可能LLM未配置）")
            
            if "llm_error" in result["data"]:
                print(f"  LLM错误: {result['data']['llm_error']}")
        else:
            print(f"✗ 情感分析失败: {result['error']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 大模型集成测试失败: {e}")
        return False


async def test_mcp_tool_calls():
    """测试MCP工具调用"""
    print("\n" + "=" * 60)
    print("测试MCP工具调用")
    print("=" * 60)
    
    try:
        app = TwitterMCPApp()
        
        # 测试状态查询工具调用
        tool_call_message = {
            "jsonrpc": "2.0",
            "id": "test-tool-call",
            "method": "tools/call",
            "params": {
                "name": "get_scraper_status",
                "arguments": {}
            }
        }
        
        response = await app.handle_mcp_message(json.dumps(tool_call_message))
        call_result = json.loads(response)
        
        if "result" in call_result:
            print("✓ 工具调用测试:")
            content = call_result["result"]["content"][0]["text"]
            status_data = json.loads(content)
            print(f"  调用成功: {status_data['success']}")
            if status_data["success"]:
                print(f"  应用运行时间: {status_data['data']['app_status'].get('uptime_seconds', 0):.1f}秒")
        else:
            print(f"✗ 工具调用失败: {call_result.get('error', {}).get('message')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ MCP工具调用测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("开始Twitter MCP爬虫功能测试")
    print("=" * 60)
    
    tests = [
        ("MCP协议", test_mcp_protocol),
        ("配置管理", test_configuration),
        ("爬虫状态", test_scraper_status),
        ("大模型集成", test_llm_integration),
        ("MCP工具调用", test_mcp_tool_calls)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP工具基本功能正常。")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
    
    return passed == total


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)

