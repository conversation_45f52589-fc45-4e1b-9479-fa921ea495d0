"""
统计分析模块
提供详细的数据分析和可视化功能
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import numpy as np
from typing import List, Dict, Any
from collections import Counter
import re


class Analytics:
    """统计分析类"""

    def __init__(self):
        pass

    def render(self, results: List[Dict[str, Any]]):
        """渲染统计分析界面"""
        st.markdown('<h1 class="main-header">📈 统计分析</h1>', unsafe_allow_html=True)

        if not results:
            st.info("暂无数据可分析")
            return

        # 创建标签页
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "📊 总体概览", "📈 趋势分析", "👥 用户分析", "🔍 内容分析", "📋 详细报告"
        ])

        with tab1:
            self.render_overview(results)

        with tab2:
            self.render_trend_analysis(results)

        with tab3:
            self.render_user_analysis(results)

        with tab4:
            self.render_content_analysis(results)

        with tab5:
            self.render_detailed_report(results)

    def render_overview(self, results: List[Dict[str, Any]]):
        """渲染总体概览"""
        st.markdown("### 📊 总体数据概览")

        # 基础统计
        stats = self.calculate_basic_stats(results)

        # 关键指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "总监测次数",
                stats['total_results'],
                delta=stats.get('results_delta', 0)
            )

        with col2:
            st.metric(
                "总推文数",
                stats['total_tweets'],
                delta=stats.get('tweets_delta', 0)
            )

        with col3:
            st.metric(
                "平均推文/次",
                f"{stats['avg_tweets_per_result']:.1f}",
                delta=f"{stats.get('avg_delta', 0):.1f}"
            )

        with col4:
            st.metric(
                "监测成功率",
                f"{stats['success_rate']:.1f}%",
                delta=f"{stats.get('success_delta', 0):.1f}%"
            )

        # 监测类型分布
        col1, col2 = st.columns(2)

        with col1:
            self.render_monitor_type_pie(results)

        with col2:
            self.render_success_rate_gauge(stats['success_rate'])

        # 时间分布热力图
        self.render_time_heatmap(results)

        # 最活跃的监测目标
        self.render_top_targets(results)

    def render_trend_analysis(self, results: List[Dict[str, Any]]):
        """渲染趋势分析"""
        st.markdown("### 📈 趋势分析")

        # 时间范围选择
        col1, col2 = st.columns(2)

        with col1:
            time_range = st.selectbox(
                "时间范围",
                ["最近24小时", "最近7天", "最近30天", "全部时间"],
                index=1
            )

        with col2:
            granularity = st.selectbox(
                "时间粒度",
                ["小时", "天", "周"],
                index=1
            )

        # 过滤数据
        filtered_results = self.filter_by_time_range(results, time_range)

        # 推文数量趋势
        self.render_tweet_count_trend(filtered_results, granularity)

        # 互动数据趋势
        self.render_engagement_trend(filtered_results, granularity)

        # 监测效率趋势
        self.render_efficiency_trend(filtered_results, granularity)

    def render_user_analysis(self, results: List[Dict[str, Any]]):
        """渲染用户分析"""
        st.markdown("### 👥 用户行为分析")

        # 提取用户数据
        user_data = self.extract_user_data(results)

        if not user_data:
            st.info("暂无用户数据")
            return

        # 最活跃用户
        col1, col2 = st.columns(2)

        with col1:
            self.render_top_users_by_tweets(user_data)

        with col2:
            self.render_top_users_by_engagement(user_data)

        # 用户互动分析
        self.render_user_engagement_scatter(user_data)

        # 用户活跃时间分析
        self.render_user_activity_timeline(user_data)

    def render_content_analysis(self, results: List[Dict[str, Any]]):
        """渲染内容分析"""
        st.markdown("### 🔍 内容分析")

        # 提取文本数据
        text_data = self.extract_text_data(results)

        if not text_data:
            st.info("暂无文本数据")
            return

        # 词频分析
        col1, col2 = st.columns(2)

        with col1:
            self.render_word_frequency(text_data)

        with col2:
            self.render_hashtag_analysis(text_data)

        # 推文长度分析
        self.render_tweet_length_analysis(text_data)

        # 情感分析（简化版）
        self.render_sentiment_analysis(text_data)

        # 热门话题
        self.render_trending_topics(text_data)

    def render_detailed_report(self, results: List[Dict[str, Any]]):
        """渲染详细报告"""
        st.markdown("### 📋 详细分析报告")

        # 报告生成选项
        col1, col2, col3 = st.columns(3)

        with col1:
            report_type = st.selectbox(
                "报告类型",
                ["综合报告", "监测效果报告", "用户行为报告", "内容分析报告"],
                index=0
            )

        with col2:
            time_period = st.selectbox(
                "时间周期",
                ["最近7天", "最近30天", "最近90天", "全部时间"],
                index=1
            )

        with col3:
            if st.button("📋 生成报告", use_container_width=True):
                self.generate_detailed_report(results, report_type, time_period)

        # 数据表格
        self.render_data_tables(results)

    def calculate_basic_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算基础统计数据"""
        total_results = len(results)
        total_tweets = sum(len(result.get('tweets', [])) for result in results)
        success_count = sum(1 for result in results if result.get('success', False))

        stats = {
            'total_results': total_results,
            'total_tweets': total_tweets,
            'avg_tweets_per_result': total_tweets / total_results if total_results > 0 else 0,
            'success_rate': success_count / total_results * 100 if total_results > 0 else 0,
            'success_count': success_count,
            'error_count': total_results - success_count
        }

        return stats

    def render_monitor_type_pie(self, results: List[Dict[str, Any]]):
        """渲染监测类型饼图"""
        type_counts = Counter(result.get('monitor_type', 'unknown') for result in results)

        fig = px.pie(
            values=list(type_counts.values()),
            names=list(type_counts.keys()),
            title="监测类型分布",
            color_discrete_map={
                'account': '#1DA1F2',
                'keyword': '#17BF63',
                'unknown': '#657786'
            }
        )

        fig.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig, use_container_width=True)

    def render_success_rate_gauge(self, success_rate: float):
        """渲染成功率仪表盘"""
        fig = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=success_rate,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "监测成功率 (%)"},
            delta={'reference': 90},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"},
                    {'range': [80, 100], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))

        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)

    def render_time_heatmap(self, results: List[Dict[str, Any]]):
        """渲染时间分布热力图"""
        st.markdown("#### ⏰ 监测活动时间分布")

        # 处理时间数据
        time_data = []
        for result in results:
            timestamp = result.get('timestamp', '')
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_data.append({
                    'hour': dt.hour,
                    'day': dt.strftime('%A'),
                    'count': len(result.get('tweets', []))
                })
            except:
                continue

        if not time_data:
            st.info("暂无时间数据")
            return

        df = pd.DataFrame(time_data)

        # 创建热力图数据
        heatmap_data = df.groupby(['day', 'hour'])['count'].sum().reset_index()
        heatmap_pivot = heatmap_data.pivot(index='day', columns='hour', values='count').fillna(0)

        # 重新排序星期
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        heatmap_pivot = heatmap_pivot.reindex(day_order)

        fig = px.imshow(
            heatmap_pivot,
            labels=dict(x="小时", y="星期", color="推文数量"),
            title="监测活动热力图",
            color_continuous_scale="Blues"
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_top_targets(self, results: List[Dict[str, Any]]):
        """渲染最活跃的监测目标"""
        st.markdown("#### 🎯 最活跃的监测目标")

        target_stats = {}
        for result in results:
            target = result.get('target', 'unknown')
            tweet_count = len(result.get('tweets', []))

            if target not in target_stats:
                target_stats[target] = {'tweets': 0, 'monitors': 0}

            target_stats[target]['tweets'] += tweet_count
            target_stats[target]['monitors'] += 1

        # 转换为DataFrame
        target_data = []
        for target, stats in target_stats.items():
            target_data.append({
                'target': target,
                'tweets': stats['tweets'],
                'monitors': stats['monitors'],
                'avg_tweets': stats['tweets'] / stats['monitors'] if stats['monitors'] > 0 else 0
            })

        df = pd.DataFrame(target_data)
        df = df.sort_values('tweets', ascending=False).head(10)

        fig = px.bar(
            df,
            x='tweets',
            y='target',
            orientation='h',
            title='推文数量排行榜 (Top 10)',
            labels={'tweets': '推文数量', 'target': '监测目标'}
        )

        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

    def render_tweet_count_trend(self, results: List[Dict[str, Any]], granularity: str):
        """渲染推文数量趋势"""
        st.markdown("#### 📈 推文数量趋势")

        # 处理时间数据
        time_data = []
        for result in results:
            timestamp = result.get('timestamp', '')
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_data.append({
                    'time': dt,
                    'tweets': len(result.get('tweets', [])),
                    'type': result.get('monitor_type', 'unknown')
                })
            except:
                continue

        if not time_data:
            st.info("暂无趋势数据")
            return

        df = pd.DataFrame(time_data)

        # 根据粒度聚合数据
        if granularity == "小时":
            df['period'] = df['time'].dt.floor('H')
        elif granularity == "天":
            df['period'] = df['time'].dt.date
        else:  # 周
            df['period'] = df['time'].dt.to_period('W').dt.start_time

        trend_data = df.groupby(['period', 'type'])['tweets'].sum().reset_index()

        fig = px.line(
            trend_data,
            x='period',
            y='tweets',
            color='type',
            title=f'推文数量趋势 (按{granularity})',
            labels={'period': '时间', 'tweets': '推文数量', 'type': '监测类型'}
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_engagement_trend(self, results: List[Dict[str, Any]], granularity: str):
        """渲染互动数据趋势"""
        st.markdown("#### 💬 互动数据趋势")

        engagement_data = []
        for result in results:
            timestamp = result.get('timestamp', '')
            tweets = result.get('tweets', [])

            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

                total_likes = sum(tweet.get('like_count', 0) for tweet in tweets)
                total_retweets = sum(tweet.get('retweet_count', 0) for tweet in tweets)
                total_replies = sum(tweet.get('reply_count', 0) for tweet in tweets)

                engagement_data.append({
                    'time': dt,
                    'likes': total_likes,
                    'retweets': total_retweets,
                    'replies': total_replies
                })
            except:
                continue

        if not engagement_data:
            st.info("暂无互动数据")
            return

        df = pd.DataFrame(engagement_data)

        # 根据粒度聚合数据
        if granularity == "小时":
            df['period'] = df['time'].dt.floor('H')
        elif granularity == "天":
            df['period'] = df['time'].dt.date
        else:  # 周
            df['period'] = df['time'].dt.to_period('W').dt.start_time

        agg_data = df.groupby('period')[['likes', 'retweets', 'replies']].sum().reset_index()

        fig = go.Figure()

        fig.add_trace(go.Scatter(
            x=agg_data['period'],
            y=agg_data['likes'],
            mode='lines+markers',
            name='点赞数',
            line=dict(color='#E1306C')
        ))

        fig.add_trace(go.Scatter(
            x=agg_data['period'],
            y=agg_data['retweets'],
            mode='lines+markers',
            name='转发数',
            line=dict(color='#1DA1F2')
        ))

        fig.add_trace(go.Scatter(
            x=agg_data['period'],
            y=agg_data['replies'],
            mode='lines+markers',
            name='回复数',
            line=dict(color='#17BF63')
        ))

        fig.update_layout(
            title=f'互动数据趋势 (按{granularity})',
            xaxis_title='时间',
            yaxis_title='互动数量',
            hovermode='x unified'
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_efficiency_trend(self, results: List[Dict[str, Any]], granularity: str):
        """渲染监测效率趋势"""
        st.markdown("#### ⚡ 监测效率趋势")

        efficiency_data = []
        for result in results:
            timestamp = result.get('timestamp', '')
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                efficiency_data.append({
                    'time': dt,
                    'success': 1 if result.get('success', False) else 0,
                    'tweet_count': len(result.get('tweets', []))
                })
            except:
                continue

        if not efficiency_data:
            st.info("暂无效率数据")
            return

        df = pd.DataFrame(efficiency_data)

        # 根据粒度聚合数据
        if granularity == "小时":
            df['period'] = df['time'].dt.floor('H')
        elif granularity == "天":
            df['period'] = df['time'].dt.date
        else:  # 周
            df['period'] = df['time'].dt.to_period('W').dt.start_time

        agg_data = df.groupby('period').agg({
            'success': ['sum', 'count'],
            'tweet_count': 'sum'
        }).reset_index()

        agg_data.columns = ['period', 'success_count', 'total_count', 'total_tweets']
        agg_data['success_rate'] = agg_data['success_count'] / agg_data['total_count'] * 100
        agg_data['avg_tweets'] = agg_data['total_tweets'] / agg_data['total_count']

        # 创建双轴图表
        fig = go.Figure()

        fig.add_trace(go.Scatter(
            x=agg_data['period'],
            y=agg_data['success_rate'],
            mode='lines+markers',
            name='成功率 (%)',
            yaxis='y',
            line=dict(color='#28a745')
        ))

        fig.add_trace(go.Scatter(
            x=agg_data['period'],
            y=agg_data['avg_tweets'],
            mode='lines+markers',
            name='平均推文数',
            yaxis='y2',
            line=dict(color='#ffc107')
        ))

        fig.update_layout(
            title=f'监测效率趋势 (按{granularity})',
            xaxis_title='时间',
            yaxis=dict(title='成功率 (%)', side='left'),
            yaxis2=dict(title='平均推文数', side='right', overlaying='y'),
            hovermode='x unified'
        )

        st.plotly_chart(fig, use_container_width=True)

    def extract_user_data(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取用户数据"""
        user_data = []
        for result in results:
            tweets = result.get('tweets', [])
            for tweet in tweets:
                user_data.append({
                    'username': tweet.get('author_username', 'unknown'),
                    'display_name': tweet.get('author_display_name', 'Unknown'),
                    'text': tweet.get('text', ''),
                    'like_count': tweet.get('like_count', 0),
                    'retweet_count': tweet.get('retweet_count', 0),
                    'reply_count': tweet.get('reply_count', 0),
                    'created_at': tweet.get('created_at', ''),
                    'monitor_type': result.get('monitor_type', 'unknown')
                })
        return user_data

    def render_top_users_by_tweets(self, user_data: List[Dict[str, Any]]):
        """渲染推文数量最多的用户"""
        st.markdown("#### 📝 推文数量排行")

        user_tweet_counts = Counter(user['username'] for user in user_data)
        top_users = user_tweet_counts.most_common(10)

        if not top_users:
            st.info("暂无用户数据")
            return

        df = pd.DataFrame(top_users, columns=['用户名', '推文数'])

        fig = px.bar(
            df,
            x='推文数',
            y='用户名',
            orientation='h',
            title='推文数量排行榜',
            color='推文数',
            color_continuous_scale='Blues'
        )

        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

    def render_top_users_by_engagement(self, user_data: List[Dict[str, Any]]):
        """渲染互动数据最高的用户"""
        st.markdown("#### 💬 互动数据排行")

        user_engagement = {}
        for user in user_data:
            username = user['username']
            if username not in user_engagement:
                user_engagement[username] = {
                    'total_likes': 0,
                    'total_retweets': 0,
                    'total_replies': 0,
                    'tweet_count': 0
                }

            user_engagement[username]['total_likes'] += user['like_count']
            user_engagement[username]['total_retweets'] += user['retweet_count']
            user_engagement[username]['total_replies'] += user['reply_count']
            user_engagement[username]['tweet_count'] += 1

        # 计算总互动数
        engagement_data = []
        for username, stats in user_engagement.items():
            total_engagement = stats['total_likes'] + stats['total_retweets'] + stats['total_replies']
            engagement_data.append({
                'username': username,
                'total_engagement': total_engagement,
                'avg_engagement': total_engagement / stats['tweet_count'] if stats['tweet_count'] > 0 else 0
            })

        # 排序并取前10
        engagement_data.sort(key=lambda x: x['total_engagement'], reverse=True)
        top_engagement = engagement_data[:10]

        if not top_engagement:
            st.info("暂无互动数据")
            return

        df = pd.DataFrame(top_engagement)

        fig = px.bar(
            df,
            x='total_engagement',
            y='username',
            orientation='h',
            title='总互动数排行榜',
            color='total_engagement',
            color_continuous_scale='Reds'
        )

        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

    def render_user_engagement_scatter(self, user_data: List[Dict[str, Any]]):
        """渲染用户互动散点图"""
        st.markdown("#### 📊 用户互动分析")

        user_stats = {}
        for user in user_data:
            username = user['username']
            if username not in user_stats:
                user_stats[username] = {
                    'tweet_count': 0,
                    'total_likes': 0,
                    'total_retweets': 0,
                    'avg_likes': 0,
                    'avg_retweets': 0
                }

            user_stats[username]['tweet_count'] += 1
            user_stats[username]['total_likes'] += user['like_count']
            user_stats[username]['total_retweets'] += user['retweet_count']

        # 计算平均值
        scatter_data = []
        for username, stats in user_stats.items():
            if stats['tweet_count'] > 0:
                scatter_data.append({
                    'username': username,
                    'tweet_count': stats['tweet_count'],
                    'avg_likes': stats['total_likes'] / stats['tweet_count'],
                    'avg_retweets': stats['total_retweets'] / stats['tweet_count'],
                    'total_engagement': stats['total_likes'] + stats['total_retweets']
                })

        if not scatter_data:
            st.info("暂无散点图数据")
            return

        df = pd.DataFrame(scatter_data)

        fig = px.scatter(
            df,
            x='avg_likes',
            y='avg_retweets',
            size='tweet_count',
            color='total_engagement',
            hover_name='username',
            title='用户互动散点图',
            labels={
                'avg_likes': '平均点赞数',
                'avg_retweets': '平均转发数',
                'tweet_count': '推文数量',
                'total_engagement': '总互动数'
            },
            color_continuous_scale='Viridis'
        )

        st.plotly_chart(fig, use_container_width=True)

    def extract_text_data(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取文本数据"""
        text_data = []
        for result in results:
            tweets = result.get('tweets', [])
            for tweet in tweets:
                text_data.append({
                    'text': tweet.get('text', ''),
                    'hashtags': tweet.get('hashtags', []),
                    'mentions': tweet.get('mentions', []),
                    'length': len(tweet.get('text', '')),
                    'like_count': tweet.get('like_count', 0),
                    'retweet_count': tweet.get('retweet_count', 0)
                })
        return text_data

    def render_word_frequency(self, text_data: List[Dict[str, Any]]):
        """渲染词频分析"""
        st.markdown("#### 📝 词频分析")

        # 简单的词频统计（这里可以集成更复杂的NLP库）
        all_text = ' '.join(item['text'] for item in text_data)

        # 简单的词汇提取（去除常见停用词）
        import re
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]{2,}\b', all_text.lower())

        # 过滤停用词
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an'}
        filtered_words = [word for word in words if word not in stop_words and len(word) > 2]

        word_freq = Counter(filtered_words)
        top_words = word_freq.most_common(20)

        if not top_words:
            st.info("暂无词频数据")
            return

        df = pd.DataFrame(top_words, columns=['词汇', '频次'])

        fig = px.bar(
            df,
            x='频次',
            y='词汇',
            orientation='h',
            title='高频词汇 (Top 20)',
            color='频次',
            color_continuous_scale='Blues'
        )

        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)

    def render_hashtag_analysis(self, text_data: List[Dict[str, Any]]):
        """渲染标签分析"""
        st.markdown("#### 🏷️ 热门标签")

        all_hashtags = []
        for item in text_data:
            all_hashtags.extend(item['hashtags'])

        if not all_hashtags:
            st.info("暂无标签数据")
            return

        hashtag_freq = Counter(all_hashtags)
        top_hashtags = hashtag_freq.most_common(15)

        df = pd.DataFrame(top_hashtags, columns=['标签', '使用次数'])

        fig = px.bar(
            df,
            x='使用次数',
            y='标签',
            orientation='h',
            title='热门标签 (Top 15)',
            color='使用次数',
            color_continuous_scale='Greens'
        )

        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

    def render_tweet_length_analysis(self, text_data: List[Dict[str, Any]]):
        """渲染推文长度分析"""
        st.markdown("#### 📏 推文长度分析")

        lengths = [item['length'] for item in text_data if item['length'] > 0]

        if not lengths:
            st.info("暂无长度数据")
            return

        fig = px.histogram(
            x=lengths,
            nbins=30,
            title='推文长度分布',
            labels={'x': '推文长度（字符数）', 'y': '频次'}
        )

        # 添加统计信息
        avg_length = sum(lengths) / len(lengths)
        fig.add_vline(x=avg_length, line_dash="dash", line_color="red",
                     annotation_text=f"平均长度: {avg_length:.1f}")

        st.plotly_chart(fig, use_container_width=True)

        # 显示统计摘要
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("平均长度", f"{avg_length:.1f}")
        with col2:
            st.metric("最短", min(lengths))
        with col3:
            st.metric("最长", max(lengths))
        with col4:
            st.metric("中位数", f"{sorted(lengths)[len(lengths)//2]}")

    def render_sentiment_analysis(self, text_data: List[Dict[str, Any]]):
        """渲染情感分析（简化版）"""
        st.markdown("#### 😊 情感分析")

        # 简化的情感分析（基于关键词）
        positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'excited', '好', '棒', '优秀', '很好', '喜欢', '开心']
        negative_words = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'disappointed', '坏', '糟糕', '讨厌', '不好', '生气', '失望']

        sentiment_data = []
        for item in text_data:
            text = item['text'].lower()
            positive_count = sum(1 for word in positive_words if word in text)
            negative_count = sum(1 for word in negative_words if word in text)

            if positive_count > negative_count:
                sentiment = '积极'
            elif negative_count > positive_count:
                sentiment = '消极'
            else:
                sentiment = '中性'

            sentiment_data.append(sentiment)

        sentiment_counts = Counter(sentiment_data)

        fig = px.pie(
            values=list(sentiment_counts.values()),
            names=list(sentiment_counts.keys()),
            title='情感分布',
            color_discrete_map={
                '积极': '#28a745',
                '消极': '#dc3545',
                '中性': '#6c757d'
            }
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_trending_topics(self, text_data: List[Dict[str, Any]]):
        """渲染热门话题"""
        st.markdown("#### 🔥 热门话题")

        # 提取所有标签
        all_hashtags = []
        for item in text_data:
            all_hashtags.extend(item['hashtags'])

        if not all_hashtags:
            st.info("暂无话题数据")
            return

        # 计算标签的总互动数
        hashtag_engagement = {}
        for item in text_data:
            for hashtag in item['hashtags']:
                if hashtag not in hashtag_engagement:
                    hashtag_engagement[hashtag] = {
                        'count': 0,
                        'total_likes': 0,
                        'total_retweets': 0
                    }

                hashtag_engagement[hashtag]['count'] += 1
                hashtag_engagement[hashtag]['total_likes'] += item['like_count']
                hashtag_engagement[hashtag]['total_retweets'] += item['retweet_count']

        # 计算热度分数（使用次数 + 互动数）
        topic_data = []
        for hashtag, stats in hashtag_engagement.items():
            heat_score = stats['count'] * 10 + stats['total_likes'] + stats['total_retweets'] * 2
            topic_data.append({
                'hashtag': hashtag,
                'count': stats['count'],
                'total_engagement': stats['total_likes'] + stats['total_retweets'],
                'heat_score': heat_score
            })

        # 排序并取前10
        topic_data.sort(key=lambda x: x['heat_score'], reverse=True)
        top_topics = topic_data[:10]

        df = pd.DataFrame(top_topics)

        fig = px.scatter(
            df,
            x='count',
            y='total_engagement',
            size='heat_score',
            hover_name='hashtag',
            title='热门话题分析',
            labels={
                'count': '使用次数',
                'total_engagement': '总互动数',
                'heat_score': '热度分数'
            },
            color='heat_score',
            color_continuous_scale='Reds'
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_data_tables(self, results: List[Dict[str, Any]]):
        """渲染数据表格"""
        st.markdown("#### 📋 原始数据")

        # 监测结果汇总表
        st.markdown("**监测结果汇总**")

        summary_data = []
        for i, result in enumerate(results):
            summary_data.append({
                '序号': i + 1,
                '监测类型': result.get('monitor_type', ''),
                '监测目标': result.get('target', ''),
                '监测时间': result.get('timestamp', '')[:19],
                '推文数量': len(result.get('tweets', [])),
                '状态': '成功' if result.get('success', False) else '失败'
            })

        summary_df = pd.DataFrame(summary_data)
        st.dataframe(summary_df, use_container_width=True)

        # 推文详情表（显示最近50条）
        st.markdown("**推文详情 (最近50条)**")

        tweet_details = []
        count = 0
        for result in reversed(results):
            if count >= 50:
                break

            tweets = result.get('tweets', [])
            for tweet in tweets:
                if count >= 50:
                    break

                tweet_details.append({
                    '监测目标': result.get('target', ''),
                    '作者': tweet.get('author_username', ''),
                    '推文内容': tweet.get('text', '')[:100] + '...' if len(tweet.get('text', '')) > 100 else tweet.get('text', ''),
                    '发布时间': tweet.get('created_at', '')[:19],
                    '点赞': tweet.get('like_count', 0),
                    '转发': tweet.get('retweet_count', 0),
                    '回复': tweet.get('reply_count', 0)
                })
                count += 1

        if tweet_details:
            tweet_df = pd.DataFrame(tweet_details)
            st.dataframe(tweet_df, use_container_width=True)
        else:
            st.info("暂无推文详情")

    def filter_by_time_range(self, results: List[Dict[str, Any]], time_range: str) -> List[Dict[str, Any]]:
        """根据时间范围过滤结果"""
        if time_range == "全部时间":
            return results

        now = datetime.now()

        if time_range == "最近24小时":
            cutoff = now - timedelta(hours=24)
        elif time_range == "最近7天":
            cutoff = now - timedelta(days=7)
        elif time_range == "最近30天":
            cutoff = now - timedelta(days=30)
        else:
            return results

        filtered = []
        for result in results:
            timestamp = result.get('timestamp', '')
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                if dt >= cutoff:
                    filtered.append(result)
            except:
                continue

        return filtered

    def render_user_activity_timeline(self, user_data: List[Dict[str, Any]]):
        """渲染用户活跃时间线"""
        st.markdown("#### ⏰ 用户活跃时间分析")

        # 处理时间数据
        activity_data = []
        for user in user_data:
            created_at = user.get('created_at', '')
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                activity_data.append({
                    'hour': dt.hour,
                    'username': user['username']
                })
            except:
                continue

        if not activity_data:
            st.info("暂无活跃时间数据")
            return

        df = pd.DataFrame(activity_data)
        hourly_activity = df.groupby('hour').size().reset_index(name='tweet_count')

        fig = px.bar(
            hourly_activity,
            x='hour',
            y='tweet_count',
            title='用户活跃时间分布',
            labels={'hour': '小时', 'tweet_count': '推文数量'}
        )

        fig.update_layout(
            xaxis=dict(tickmode='linear', tick0=0, dtick=2),
            height=300
        )

        st.plotly_chart(fig, use_container_width=True)

    def generate_detailed_report(self, results: List[Dict[str, Any]], report_type: str, time_period: str):
        """生成详细报告"""
        st.markdown(f"#### 📋 {report_type} - {time_period}")

        # 过滤数据
        filtered_results = self.filter_by_time_range(results, time_period)

        if not filtered_results:
            st.warning("所选时间范围内无数据")
            return

        # 生成报告内容
        stats = self.calculate_basic_stats(filtered_results)

        st.markdown("**报告摘要**")

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("监测次数", stats['total_results'])
        with col2:
            st.metric("推文总数", stats['total_tweets'])
        with col3:
            st.metric("成功率", f"{stats['success_rate']:.1f}%")

        # 根据报告类型显示不同内容
        if report_type == "综合报告":
            st.markdown("**监测类型分布**")
            type_counts = Counter(result.get('monitor_type', 'unknown') for result in filtered_results)
            st.json(dict(type_counts))

            st.markdown("**热门监测目标**")
            target_counts = Counter(result.get('target', 'unknown') for result in filtered_results)
            top_targets = dict(target_counts.most_common(5))
            st.json(top_targets)

        elif report_type == "监测效果报告":
            success_by_type = {}
            for result in filtered_results:
                monitor_type = result.get('monitor_type', 'unknown')
                if monitor_type not in success_by_type:
                    success_by_type[monitor_type] = {'success': 0, 'total': 0}

                success_by_type[monitor_type]['total'] += 1
                if result.get('success', False):
                    success_by_type[monitor_type]['success'] += 1

            st.markdown("**各类型监测成功率**")
            for monitor_type, stats in success_by_type.items():
                rate = stats['success'] / stats['total'] * 100 if stats['total'] > 0 else 0
                st.text(f"{monitor_type}: {rate:.1f}% ({stats['success']}/{stats['total']})")

        # 可以继续添加其他报告类型的逻辑

        st.success("✅ 报告生成完成")