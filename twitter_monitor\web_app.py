#!/usr/bin/env python3
"""
Twitter监测脚本Web可视化界面
基于Streamlit构建的Web应用
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import time
import threading
import sys
from pathlib import Path
import io
import base64

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from core import TwitterMonitor
from utils import setup_logger
from web_modules.monitor_manager import MonitorManager
from web_modules.data_exporter import DataExporter
from web_modules.dingtalk_bot import DingTalkBot
from web_modules.dashboard import Dashboard

# 页面配置
st.set_page_config(
    page_title="Twitter监测系统",
    page_icon="🐦",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1DA1F2;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1DA1F2;
    }
    .status-running {
        color: #28a745;
        font-weight: bold;
    }
    .status-stopped {
        color: #dc3545;
        font-weight: bold;
    }
    .tweet-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e1e8ed;
        margin-bottom: 1rem;
    }
    .sidebar-section {
        margin-bottom: 2rem;
    }
</style>
""", unsafe_allow_html=True)

class TwitterMonitorWebApp:
    """Twitter监测Web应用主类"""
    
    def __init__(self):
        self.init_session_state()
        self.config = Config()
        self.monitor_manager = MonitorManager()
        self.data_exporter = DataExporter()
        self.dingtalk_bot = DingTalkBot()
        self.dashboard = Dashboard()
        
        # 设置日志
        self.logger = setup_logger('web_app')
    
    def init_session_state(self):
        """初始化会话状态"""
        if 'monitor_running' not in st.session_state:
            st.session_state.monitor_running = False
        
        if 'monitor_results' not in st.session_state:
            st.session_state.monitor_results = []
        
        if 'config_changed' not in st.session_state:
            st.session_state.config_changed = False
        
        if 'last_update' not in st.session_state:
            st.session_state.last_update = datetime.now()
        
        if 'total_tweets' not in st.session_state:
            st.session_state.total_tweets = 0
        
        if 'monitor_stats' not in st.session_state:
            st.session_state.monitor_stats = {
                'success_count': 0,
                'error_count': 0,
                'accounts_monitored': 0,
                'keywords_monitored': 0
            }
    
    def render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.markdown("## 🐦 Twitter监测系统")
        st.sidebar.markdown("---")
        
        # 导航菜单
        page = st.sidebar.selectbox(
            "📋 选择功能页面",
            ["🏠 监控面板", "⚙️ 监测配置", "📊 结果展示", "📤 数据导出", "🔔 钉钉推送", "📈 统计分析"]
        )
        
        st.sidebar.markdown("---")
        
        # 快速状态
        self.render_quick_status()
        
        # 快速操作
        self.render_quick_actions()
        
        return page
    
    def render_quick_status(self):
        """渲染快速状态"""
        st.sidebar.markdown("### 📊 快速状态")
        
        # 监测状态
        if st.session_state.monitor_running:
            st.sidebar.markdown('<p class="status-running">🟢 监测运行中</p>', unsafe_allow_html=True)
        else:
            st.sidebar.markdown('<p class="status-stopped">🔴 监测已停止</p>', unsafe_allow_html=True)
        
        # 统计信息
        stats = st.session_state.monitor_stats
        st.sidebar.metric("📱 监测账号", stats['accounts_monitored'])
        st.sidebar.metric("🔍 监测关键词", stats['keywords_monitored'])
        st.sidebar.metric("📝 总推文数", st.session_state.total_tweets)
        
        # 最后更新时间
        st.sidebar.text(f"🕒 更新: {st.session_state.last_update.strftime('%H:%M:%S')}")
    
    def render_quick_actions(self):
        """渲染快速操作"""
        st.sidebar.markdown("### ⚡ 快速操作")
        
        col1, col2 = st.sidebar.columns(2)
        
        with col1:
            if st.button("▶️ 开始", key="start_monitor"):
                self.start_monitoring()
        
        with col2:
            if st.button("⏹️ 停止", key="stop_monitor"):
                self.stop_monitoring()
        
        if st.button("🔄 刷新数据", key="refresh_data"):
            self.refresh_data()
        
        if st.button("📋 导出报告", key="export_report"):
            self.export_quick_report()
    
    def start_monitoring(self):
        """开始监测"""
        try:
            if not st.session_state.monitor_running:
                self.monitor_manager.start_monitoring()
                st.session_state.monitor_running = True
                st.success("✅ 监测已启动")
                self.logger.info("Web界面启动监测")
        except Exception as e:
            st.error(f"❌ 启动监测失败: {e}")
            self.logger.error(f"启动监测失败: {e}")
    
    def stop_monitoring(self):
        """停止监测"""
        try:
            if st.session_state.monitor_running:
                self.monitor_manager.stop_monitoring()
                st.session_state.monitor_running = False
                st.success("✅ 监测已停止")
                self.logger.info("Web界面停止监测")
        except Exception as e:
            st.error(f"❌ 停止监测失败: {e}")
            self.logger.error(f"停止监测失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            # 更新监测结果
            new_results = self.monitor_manager.get_recent_results()
            st.session_state.monitor_results.extend(new_results)
            
            # 更新统计信息
            stats = self.monitor_manager.get_status()
            st.session_state.monitor_stats.update(stats)
            
            # 更新总推文数
            st.session_state.total_tweets = sum(len(result.get('tweets', [])) for result in st.session_state.monitor_results)
            
            # 更新时间
            st.session_state.last_update = datetime.now()
            
            st.success("✅ 数据已刷新")
            
        except Exception as e:
            st.error(f"❌ 刷新数据失败: {e}")
            self.logger.error(f"刷新数据失败: {e}")
    
    def export_quick_report(self):
        """快速导出报告"""
        try:
            if st.session_state.monitor_results:
                report_data = self.data_exporter.generate_summary_report(st.session_state.monitor_results)
                
                # 创建下载链接
                json_str = json.dumps(report_data, ensure_ascii=False, indent=2)
                b64 = base64.b64encode(json_str.encode()).decode()
                href = f'<a href="data:application/json;base64,{b64}" download="twitter_monitor_report.json">📋 下载监测报告</a>'
                st.sidebar.markdown(href, unsafe_allow_html=True)
                
                st.success("✅ 报告已生成")
            else:
                st.warning("⚠️ 暂无数据可导出")
                
        except Exception as e:
            st.error(f"❌ 导出报告失败: {e}")
            self.logger.error(f"导出报告失败: {e}")
    
    def render_dashboard(self):
        """渲染监控面板"""
        st.markdown('<h1 class="main-header">🏠 Twitter监测控制面板</h1>', unsafe_allow_html=True)
        
        # 主要指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="📱 监测账号",
                value=st.session_state.monitor_stats['accounts_monitored'],
                delta=None
            )
        
        with col2:
            st.metric(
                label="🔍 监测关键词", 
                value=st.session_state.monitor_stats['keywords_monitored'],
                delta=None
            )
        
        with col3:
            st.metric(
                label="📝 总推文数",
                value=st.session_state.total_tweets,
                delta=len(st.session_state.monitor_results[-1].get('tweets', [])) if st.session_state.monitor_results else 0
            )
        
        with col4:
            success_rate = 0
            if st.session_state.monitor_stats['success_count'] + st.session_state.monitor_stats['error_count'] > 0:
                success_rate = st.session_state.monitor_stats['success_count'] / (
                    st.session_state.monitor_stats['success_count'] + st.session_state.monitor_stats['error_count']
                ) * 100
            
            st.metric(
                label="✅ 成功率",
                value=f"{success_rate:.1f}%",
                delta=None
            )
        
        st.markdown("---")
        
        # 实时监测状态
        self.dashboard.render_realtime_status()
        
        # 最新推文展示
        self.dashboard.render_latest_tweets(st.session_state.monitor_results)
    
    def run(self):
        """运行Web应用"""
        # 渲染侧边栏并获取选择的页面
        selected_page = self.render_sidebar()
        
        # 根据选择的页面渲染对应内容
        if selected_page == "🏠 监控面板":
            self.render_dashboard()
        
        elif selected_page == "⚙️ 监测配置":
            from web_modules.config_manager import ConfigManager
            config_manager = ConfigManager(self.config)
            config_manager.render()
        
        elif selected_page == "📊 结果展示":
            from web_modules.results_viewer import ResultsViewer
            results_viewer = ResultsViewer()
            results_viewer.render(st.session_state.monitor_results)
        
        elif selected_page == "📤 数据导出":
            self.data_exporter.render(st.session_state.monitor_results)
        
        elif selected_page == "🔔 钉钉推送":
            self.dingtalk_bot.render()
        
        elif selected_page == "📈 统计分析":
            from web_modules.analytics import Analytics
            analytics = Analytics()
            analytics.render(st.session_state.monitor_results)


def main():
    """主函数"""
    try:
        app = TwitterMonitorWebApp()
        app.run()
        
    except Exception as e:
        st.error(f"❌ 应用启动失败: {e}")
        st.exception(e)


if __name__ == "__main__":
    main()
