"""
日志管理工具
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime


def setup_logger(name: str = None, config: Dict[str, Any] = None) -> logging.Logger:
    """设置日志记录器"""
    
    # 默认配置
    default_config = {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file": "twitter-mcp.log",
        "max_size": "10MB",
        "backup_count": 5,
        "console": True
    }
    
    # 合并配置
    if config:
        default_config.update(config)
    
    # 获取或创建logger
    logger_name = name or __name__
    logger = logging.getLogger(logger_name)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    # 设置日志级别
    level = getattr(logging, default_config["level"].upper(), logging.INFO)
    logger.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter(
        default_config["format"],
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # 控制台处理器
    if default_config.get("console", True):
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if default_config.get("file"):
        # 确保日志目录存在
        log_file = Path(default_config["file"])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 解析文件大小
        max_size = _parse_size(default_config.get("max_size", "10MB"))
        backup_count = default_config.get("backup_count", 5)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 防止日志向上传播
    logger.propagate = False
    
    return logger


def _parse_size(size_str: str) -> int:
    """解析文件大小字符串"""
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        # 假设是字节数
        return int(size_str)


class ContextFilter(logging.Filter):
    """上下文过滤器，添加额外的上下文信息"""
    
    def __init__(self, context: Dict[str, Any] = None):
        super().__init__()
        self.context = context or {}
    
    def filter(self, record):
        # 添加上下文信息到日志记录
        for key, value in self.context.items():
            setattr(record, key, value)
        return True


class TwitterMCPLogger:
    """Twitter MCP专用日志管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.loggers = {}
        self.session_id = None
        self.start_time = datetime.now()
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志记录器"""
        if name not in self.loggers:
            self.loggers[name] = setup_logger(name, self.config)
            
            # 添加上下文过滤器
            context_filter = ContextFilter({
                'session_id': self.session_id,
                'component': name.split('.')[-1]
            })
            self.loggers[name].addFilter(context_filter)
        
        return self.loggers[name]
    
    def set_session_id(self, session_id: str):
        """设置会话ID"""
        self.session_id = session_id
        
        # 更新所有现有logger的上下文
        for logger in self.loggers.values():
            for filter_obj in logger.filters:
                if isinstance(filter_obj, ContextFilter):
                    filter_obj.context['session_id'] = session_id
    
    def log_request(self, method: str, params: Dict[str, Any], 
                   response_time: float = None):
        """记录请求日志"""
        logger = self.get_logger('twitter_mcp.request')
        
        log_data = {
            'method': method,
            'params': self._sanitize_params(params),
            'timestamp': datetime.now().isoformat()
        }
        
        if response_time is not None:
            log_data['response_time'] = f"{response_time:.3f}s"
        
        logger.info(f"请求: {method}", extra=log_data)
    
    def log_scraper_activity(self, activity: str, details: Dict[str, Any] = None):
        """记录爬虫活动日志"""
        logger = self.get_logger('twitter_mcp.scraper')
        
        log_data = {
            'activity': activity,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            log_data.update(self._sanitize_params(details))
        
        logger.info(f"爬虫活动: {activity}", extra=log_data)
    
    def log_anti_block_event(self, event: str, details: Dict[str, Any] = None):
        """记录反屏蔽事件日志"""
        logger = self.get_logger('twitter_mcp.anti_block')
        
        log_data = {
            'event': event,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            log_data.update(details)
        
        logger.warning(f"反屏蔽事件: {event}", extra=log_data)
    
    def log_error(self, component: str, error: Exception, 
                  context: Dict[str, Any] = None):
        """记录错误日志"""
        logger = self.get_logger(f'twitter_mcp.{component}')
        
        log_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat()
        }
        
        if context:
            log_data.update(self._sanitize_params(context))
        
        logger.error(f"错误: {error}", extra=log_data, exc_info=True)
    
    def _sanitize_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """清理参数中的敏感信息"""
        sanitized = {}
        sensitive_keys = ['password', 'api_key', 'token', 'secret', 'auth']
        
        for key, value in params.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = '***'
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_params(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self._sanitize_params(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value
        
        return sanitized
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'start_time': self.start_time.isoformat(),
            'uptime': str(datetime.now() - self.start_time),
            'session_id': self.session_id,
            'loggers_count': len(self.loggers),
            'loggers': list(self.loggers.keys())
        }
        
        return stats


# 全局日志管理器实例
_global_logger_manager = None


def get_logger_manager(config: Dict[str, Any] = None) -> TwitterMCPLogger:
    """获取全局日志管理器实例"""
    global _global_logger_manager
    
    if _global_logger_manager is None:
        _global_logger_manager = TwitterMCPLogger(config)
    
    return _global_logger_manager


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    manager = get_logger_manager()
    return manager.get_logger(name)


# 测试代码
if __name__ == "__main__":
    # 测试基本日志功能
    logger = setup_logger("test_logger")
    
    logger.debug("这是调试信息")
    logger.info("这是信息日志")
    logger.warning("这是警告日志")
    logger.error("这是错误日志")
    
    # 测试Twitter MCP日志管理器
    print("\n测试Twitter MCP日志管理器:")
    
    log_manager = TwitterMCPLogger({
        "level": "DEBUG",
        "file": "test.log",
        "console": True
    })
    
    log_manager.set_session_id("test-session-123")
    
    # 测试不同类型的日志
    log_manager.log_request("scrape_user_tweets", {
        "username": "test_user",
        "password": "secret123",
        "count": 20
    }, 1.234)
    
    log_manager.log_scraper_activity("开始爬取", {
        "target": "test_user",
        "count": 20
    })
    
    log_manager.log_anti_block_event("代理轮换", {
        "old_proxy": "1.2.3.4:8080",
        "new_proxy": "5.6.7.8:8080"
    })
    
    try:
        raise ValueError("测试错误")
    except Exception as e:
        log_manager.log_error("scraper", e, {
            "context": "测试错误日志",
            "api_key": "secret_key_123"
        })
    
    # 获取统计信息
    stats = log_manager.get_log_stats()
    print(f"\n日志统计: {stats}")

