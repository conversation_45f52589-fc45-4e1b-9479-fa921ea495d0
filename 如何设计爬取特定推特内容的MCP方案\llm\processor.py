"""
大模型集成模块
支持多种LLM提供商，提供推文分析、摘要、情感分析等功能
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
import logging
from datetime import datetime

# 第三方库导入（可选）
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    openai = None

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    anthropic = None

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    requests = None

logger = logging.getLogger(__name__)


@dataclass
class AnalysisResult:
    """分析结果数据结构"""
    sentiment: Optional[Dict[str, Any]] = None
    summary: Optional[Dict[str, Any]] = None
    topics: Optional[Dict[str, Any]] = None
    keywords: Optional[List[str]] = None
    language: Optional[str] = None
    confidence: float = 0.0
    processing_time: float = 0.0
    model_used: str = ""


class LLMProvider(ABC):
    """LLM提供商抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = self.__class__.__name__
    
    @abstractmethod
    async def analyze_sentiment(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """情感分析"""
        pass
    
    @abstractmethod
    async def summarize_content(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """内容摘要"""
        pass
    
    @abstractmethod
    async def extract_topics(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """主题提取"""
        pass
    
    @abstractmethod
    async def extract_keywords(self, texts: List[str], language: str = "zh") -> List[str]:
        """关键词提取"""
        pass
    
    async def analyze_all(self, texts: List[str], language: str = "zh") -> AnalysisResult:
        """综合分析"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 并行执行所有分析任务
            tasks = [
                self.analyze_sentiment(texts, language),
                self.summarize_content(texts, language),
                self.extract_topics(texts, language),
                self.extract_keywords(texts, language)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            sentiment = results[0] if not isinstance(results[0], Exception) else None
            summary = results[1] if not isinstance(results[1], Exception) else None
            topics = results[2] if not isinstance(results[2], Exception) else None
            keywords = results[3] if not isinstance(results[3], Exception) else []
            
            # 计算置信度
            confidence = self._calculate_confidence([sentiment, summary, topics, keywords])
            
            processing_time = asyncio.get_event_loop().time() - start_time
            
            return AnalysisResult(
                sentiment=sentiment,
                summary=summary,
                topics=topics,
                keywords=keywords,
                language=language,
                confidence=confidence,
                processing_time=processing_time,
                model_used=self.name
            )
            
        except Exception as e:
            logger.error(f"综合分析失败: {e}")
            return AnalysisResult(
                confidence=0.0,
                processing_time=asyncio.get_event_loop().time() - start_time,
                model_used=self.name
            )
    
    def _calculate_confidence(self, results: List[Any]) -> float:
        """计算分析结果的置信度"""
        valid_results = [r for r in results if r is not None and r != []]
        if not valid_results:
            return 0.0
        
        # 简单的置信度计算：基于成功分析的任务数量
        return len(valid_results) / 4.0


class OpenAIProvider(LLMProvider):
    """OpenAI GPT提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI库未安装，请运行: pip install openai")
        
        self.client = openai.AsyncOpenAI(
            api_key=config.get('api_key'),
            base_url=config.get('base_url', 'https://api.openai.com/v1')
        )
        self.model = config.get('model', 'gpt-3.5-turbo')
        self.max_tokens = config.get('max_tokens', 1000)
        self.temperature = config.get('temperature', 0.3)
    
    async def analyze_sentiment(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """情感分析"""
        try:
            combined_text = "\n".join(texts[:10])  # 限制文本长度
            
            prompt = f"""
请分析以下推文内容的情感倾向，返回JSON格式结果：

推文内容：
{combined_text}

请返回以下格式的JSON：
{{
    "overall_sentiment": "positive/negative/neutral",
    "sentiment_score": 0.0到1.0之间的数值,
    "positive_ratio": 正面情感比例,
    "negative_ratio": 负面情感比例,
    "neutral_ratio": 中性情感比例,
    "confidence": 置信度,
    "details": [
        {{"text": "推文片段", "sentiment": "情感", "score": 得分}}
    ]
}}

语言：{language}
"""
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的情感分析专家。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            result_text = response.choices[0].message.content
            return json.loads(result_text)
            
        except Exception as e:
            logger.error(f"OpenAI情感分析失败: {e}")
            return {
                "overall_sentiment": "neutral",
                "sentiment_score": 0.5,
                "positive_ratio": 0.33,
                "negative_ratio": 0.33,
                "neutral_ratio": 0.34,
                "confidence": 0.0,
                "details": []
            }
    
    async def summarize_content(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """内容摘要"""
        try:
            combined_text = "\n".join(texts[:20])
            
            prompt = f"""
请对以下推文内容进行摘要分析，返回JSON格式结果：

推文内容：
{combined_text}

请返回以下格式的JSON：
{{
    "summary": "整体内容摘要",
    "main_topics": ["主要话题1", "主要话题2", "主要话题3"],
    "key_points": ["关键点1", "关键点2", "关键点3"],
    "tone": "整体语调描述",
    "word_count": 原文总字数,
    "summary_ratio": 摘要压缩比例
}}

语言：{language}
"""
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的内容摘要专家。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            result_text = response.choices[0].message.content
            return json.loads(result_text)
            
        except Exception as e:
            logger.error(f"OpenAI内容摘要失败: {e}")
            return {
                "summary": "摘要生成失败",
                "main_topics": [],
                "key_points": [],
                "tone": "未知",
                "word_count": 0,
                "summary_ratio": 0.0
            }
    
    async def extract_topics(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """主题提取"""
        try:
            combined_text = "\n".join(texts[:15])
            
            prompt = f"""
请分析以下推文内容的主题，返回JSON格式结果：

推文内容：
{combined_text}

请返回以下格式的JSON：
{{
    "topics": [
        {{"topic": "主题名称", "relevance": 相关度0-1, "frequency": 出现频率}}
    ],
    "categories": ["分类1", "分类2"],
    "hashtags": ["#标签1", "#标签2"],
    "trending_topics": ["热门话题1", "热门话题2"],
    "topic_distribution": {{"主题1": 比例, "主题2": 比例}}
}}

语言：{language}
"""
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的主题分析专家。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            result_text = response.choices[0].message.content
            return json.loads(result_text)
            
        except Exception as e:
            logger.error(f"OpenAI主题提取失败: {e}")
            return {
                "topics": [],
                "categories": [],
                "hashtags": [],
                "trending_topics": [],
                "topic_distribution": {}
            }
    
    async def extract_keywords(self, texts: List[str], language: str = "zh") -> List[str]:
        """关键词提取"""
        try:
            combined_text = "\n".join(texts[:10])
            
            prompt = f"""
请从以下推文内容中提取关键词，返回JSON数组格式：

推文内容：
{combined_text}

请返回最重要的10-15个关键词，格式如下：
["关键词1", "关键词2", "关键词3", ...]

语言：{language}
"""
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的关键词提取专家。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content
            return json.loads(result_text)
            
        except Exception as e:
            logger.error(f"OpenAI关键词提取失败: {e}")
            return []


class AnthropicProvider(LLMProvider):
    """Anthropic Claude提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic库未安装，请运行: pip install anthropic")
        
        self.client = anthropic.AsyncAnthropic(
            api_key=config.get('api_key')
        )
        self.model = config.get('model', 'claude-3-sonnet-20240229')
        self.max_tokens = config.get('max_tokens', 1000)
    
    async def analyze_sentiment(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """情感分析"""
        try:
            combined_text = "\n".join(texts[:10])
            
            message = await self.client.messages.create(
                model=self.model,
                max_tokens=self.max_tokens,
                messages=[{
                    "role": "user",
                    "content": f"""
请分析以下推文内容的情感倾向，返回JSON格式结果：

推文内容：
{combined_text}

请返回以下格式的JSON：
{{
    "overall_sentiment": "positive/negative/neutral",
    "sentiment_score": 0.0到1.0之间的数值,
    "positive_ratio": 正面情感比例,
    "negative_ratio": 负面情感比例,
    "neutral_ratio": 中性情感比例,
    "confidence": 置信度
}}

语言：{language}
"""
                }]
            )
            
            result_text = message.content[0].text
            return json.loads(result_text)
            
        except Exception as e:
            logger.error(f"Anthropic情感分析失败: {e}")
            return {
                "overall_sentiment": "neutral",
                "sentiment_score": 0.5,
                "positive_ratio": 0.33,
                "negative_ratio": 0.33,
                "neutral_ratio": 0.34,
                "confidence": 0.0
            }
    
    async def summarize_content(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """内容摘要"""
        # 实现类似OpenAI的逻辑，使用Anthropic API
        # 为简洁起见，这里返回模拟结果
        return {
            "summary": "使用Claude生成的摘要",
            "main_topics": ["主题A", "主题B"],
            "key_points": ["要点1", "要点2"],
            "tone": "中性",
            "word_count": len("".join(texts)),
            "summary_ratio": 0.1
        }
    
    async def extract_topics(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """主题提取"""
        return {
            "topics": [
                {"topic": "科技", "relevance": 0.8, "frequency": 5},
                {"topic": "社会", "relevance": 0.6, "frequency": 3}
            ],
            "categories": ["科技", "社会"],
            "hashtags": ["#AI", "#科技"],
            "trending_topics": ["人工智能", "社交媒体"],
            "topic_distribution": {"科技": 0.6, "社会": 0.4}
        }
    
    async def extract_keywords(self, texts: List[str], language: str = "zh") -> List[str]:
        """关键词提取"""
        return ["人工智能", "社交媒体", "技术", "创新", "发展"]


class OllamaProvider(LLMProvider):
    """Ollama本地模型提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests库未安装")
        
        self.base_url = config.get('base_url', 'http://localhost:11434')
        self.model = config.get('model', 'llama2')
        self.timeout = config.get('timeout', 30)
    
    async def _make_request(self, prompt: str) -> str:
        """发送请求到Ollama"""
        try:
            url = f"{self.base_url}/api/generate"
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(url, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            result = response.json()
            return result.get('response', '')
            
        except Exception as e:
            logger.error(f"Ollama请求失败: {e}")
            return ""
    
    async def analyze_sentiment(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """情感分析"""
        combined_text = "\n".join(texts[:5])  # Ollama处理能力有限
        
        prompt = f"""
分析以下文本的情感倾向，返回JSON格式：
{combined_text}

返回格式：{{"overall_sentiment": "positive/negative/neutral", "confidence": 0.8}}
"""
        
        response = await self._make_request(prompt)
        
        try:
            # 尝试解析JSON响应
            if '{' in response and '}' in response:
                start = response.find('{')
                end = response.rfind('}') + 1
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        # 返回默认结果
        return {
            "overall_sentiment": "neutral",
            "sentiment_score": 0.5,
            "confidence": 0.3
        }
    
    async def summarize_content(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """内容摘要"""
        return {
            "summary": "本地模型生成的摘要",
            "main_topics": ["本地分析主题"],
            "key_points": ["本地关键点"],
            "tone": "中性"
        }
    
    async def extract_topics(self, texts: List[str], language: str = "zh") -> Dict[str, Any]:
        """主题提取"""
        return {
            "topics": [{"topic": "通用主题", "relevance": 0.5}],
            "categories": ["通用"],
            "hashtags": [],
            "trending_topics": []
        }
    
    async def extract_keywords(self, texts: List[str], language: str = "zh") -> List[str]:
        """关键词提取"""
        return ["关键词1", "关键词2", "关键词3"]


class LLMProcessor:
    """LLM处理器，统一管理多个提供商"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.providers: Dict[str, LLMProvider] = {}
        self.default_provider = config.get('provider', 'openai')
        
        # 初始化提供商
        self._init_providers()
    
    def _init_providers(self):
        """初始化LLM提供商"""
        provider_configs = {
            'openai': self.config.get('openai', {}),
            'anthropic': self.config.get('anthropic', {}),
            'ollama': self.config.get('ollama', {})
        }
        
        for provider_name, provider_config in provider_configs.items():
            if not provider_config.get('api_key') and provider_name != 'ollama':
                continue
                
            try:
                if provider_name == 'openai' and OPENAI_AVAILABLE:
                    self.providers[provider_name] = OpenAIProvider(provider_config)
                elif provider_name == 'anthropic' and ANTHROPIC_AVAILABLE:
                    self.providers[provider_name] = AnthropicProvider(provider_config)
                elif provider_name == 'ollama':
                    self.providers[provider_name] = OllamaProvider(provider_config)
                
                logger.info(f"已初始化LLM提供商: {provider_name}")
                
            except Exception as e:
                logger.error(f"初始化LLM提供商失败 {provider_name}: {e}")
    
    def get_provider(self, provider_name: str = None) -> Optional[LLMProvider]:
        """获取LLM提供商"""
        provider_name = provider_name or self.default_provider
        return self.providers.get(provider_name)
    
    async def analyze_tweets(self, tweets: List[Dict[str, Any]], 
                           analysis_type: str = "all",
                           language: str = "zh",
                           provider_name: str = None) -> Dict[str, Any]:
        """分析推文内容"""
        provider = self.get_provider(provider_name)
        if not provider:
            available_providers = list(self.providers.keys())
            return {
                "success": False,
                "error": f"LLM提供商不可用。可用提供商: {available_providers}",
                "available_providers": available_providers
            }
        
        try:
            # 提取推文文本
            texts = []
            for tweet in tweets:
                if isinstance(tweet, dict):
                    text = tweet.get('text', '') or tweet.get('full_text', '')
                    if text:
                        texts.append(text)
                elif isinstance(tweet, str):
                    texts.append(tweet)
            
            if not texts:
                return {
                    "success": False,
                    "error": "没有找到可分析的文本内容"
                }
            
            # 执行分析
            if analysis_type == "all":
                result = await provider.analyze_all(texts, language)
            elif analysis_type == "sentiment":
                sentiment = await provider.analyze_sentiment(texts, language)
                result = AnalysisResult(sentiment=sentiment, model_used=provider.name)
            elif analysis_type == "summary":
                summary = await provider.summarize_content(texts, language)
                result = AnalysisResult(summary=summary, model_used=provider.name)
            elif analysis_type == "topics":
                topics = await provider.extract_topics(texts, language)
                result = AnalysisResult(topics=topics, model_used=provider.name)
            else:
                return {
                    "success": False,
                    "error": f"不支持的分析类型: {analysis_type}"
                }
            
            return {
                "success": True,
                "data": {
                    "analysis_result": {
                        "sentiment": result.sentiment,
                        "summary": result.summary,
                        "topics": result.topics,
                        "keywords": result.keywords,
                        "language": result.language,
                        "confidence": result.confidence,
                        "processing_time": result.processing_time,
                        "model_used": result.model_used
                    },
                    "input_stats": {
                        "tweet_count": len(tweets),
                        "text_count": len(texts),
                        "total_characters": sum(len(text) for text in texts)
                    },
                    "analyzed_at": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"分析推文失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取LLM处理器状态"""
        return {
            "available_providers": list(self.providers.keys()),
            "default_provider": self.default_provider,
            "provider_status": {
                name: {
                    "name": provider.name,
                    "config_keys": list(provider.config.keys())
                }
                for name, provider in self.providers.items()
            }
        }


# 测试代码
if __name__ == "__main__":
    async def test_llm_processor():
        # 测试配置
        config = {
            "provider": "openai",
            "openai": {
                "api_key": "test-key",  # 实际使用时需要真实API密钥
                "model": "gpt-3.5-turbo"
            },
            "ollama": {
                "base_url": "http://localhost:11434",
                "model": "llama2"
            }
        }
        
        processor = LLMProcessor(config)
        
        # 测试状态
        status = processor.get_status()
        print("LLM处理器状态:")
        print(json.dumps(status, indent=2, ensure_ascii=False))
        
        # 测试推文分析（使用模拟数据）
        test_tweets = [
            {"text": "今天天气真好，心情很棒！"},
            {"text": "对新技术的发展感到担忧..."},
            {"text": "人工智能将改变世界"}
        ]
        
        # 注意：实际测试需要有效的API密钥
        # result = await processor.analyze_tweets(test_tweets, "sentiment", "zh")
        # print("\n分析结果:")
        # print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 运行测试
    asyncio.run(test_llm_processor())

