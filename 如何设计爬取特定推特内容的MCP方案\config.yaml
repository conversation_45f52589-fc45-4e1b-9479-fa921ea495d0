# Twitter MCP 爬虫配置文件

# Twitter 账户配置
twitter:
  username: "RayXR_"  # 你的Twitter用户名
  password: "WXRaldy520!"  # 你的Twitter密码
  cookies_path: "cookies.json"  # cookies保存路径

# 爬虫配置
scraper:
  default_count: 20  # 默认爬取数量
  max_count: 100     # 最大爬取数量
  default_max_age_days: 30  # 默认最大时间范围（天）
  request_delay: 2   # 请求间隔（秒）
  daily_request_limit: 1000  # 每日请求限制

# 反屏蔽配置
anti_block:
  requests_per_minute: 30  # 每分钟请求数限制
  burst_limit: 5           # 突发请求限制
  proxies: []              # 代理列表，格式: ["http://proxy1:port", "socks5://proxy2:port"]
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# 大模型配置
llm:
  provider: "openai"  # 默认提供商: openai, anthropic, ollama
  
  # OpenAI 配置
  openai:
    api_key: "sk-zzuZvYx7Vs7y9yMjAd2eA187D0904d85AaE3D861162440E1"  # 你的OpenAI API密钥
    model: "gemini-2.5-flash-lite-preview-06-17"
    base_url: "https://api.gpt.ge/v1"
    max_tokens: 1000
    temperature: 0.3
  
  # Anthropic 配置
  anthropic:
    api_key: ""  # 你的Anthropic API密钥
    model: "claude-3-sonnet-20240229"
    max_tokens: 1000
  
  # Ollama 本地模型配置
  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"
    timeout: 30

# MCP 服务器配置
mcp:
  server_name: "Twitter MCP Scraper"
  version: "1.0.0"
  protocol_version: "2024-11-05"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "twitter-mcp.log"
  max_size: "10MB"
  backup_count: 5
  console: true

