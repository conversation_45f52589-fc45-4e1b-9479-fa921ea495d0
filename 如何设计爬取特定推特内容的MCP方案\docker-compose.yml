version: '3.8'

services:
  twitter-mcp:
    build: .
    container_name: twitter-mcp-scraper
    ports:
      - "8000:8000"
    volumes:
      - ./config.yaml:/app/config.yaml:ro
      - ./logs:/app/logs
      - ./data:/app/data
      - ./cookies.json:/app/cookies.json
    environment:
      - PYTHONPATH=/app/src
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选: Redis缓存
  redis:
    image: redis:7-alpine
    container_name: twitter-mcp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 可选: Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: twitter-mcp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - twitter-mcp
    restart: unless-stopped

volumes:
  redis_data:

