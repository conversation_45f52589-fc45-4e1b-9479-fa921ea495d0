"""
Twitter MCP 爬虫主应用程序
整合MCP服务器和Twitter爬虫功能
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from typing import Dict, Any, List
import logging
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from server import MCPServer
from scraper.engine import TwitterScraper, TweetData
from scraper.anti_block import AntiBlockManager
from llm.processor import LLMProcessor
from utils.config import Config
from utils.logger import setup_logger

# 设置日志
logger = setup_logger(__name__)


class TwitterMCPApp:
    """Twitter MCP应用程序主类"""
    
    def __init__(self, config_path: str = None):
        self.config = Config(config_path)
        self.mcp_server = MCPServer()
        self.twitter_scraper = TwitterScraper(self.config.scraper)
        self.anti_block_manager = AntiBlockManager(self.config.anti_block)
        self.llm_processor = LLMProcessor(self.config.llm)
        
        # 注册工具处理器
        self._register_handlers()
        
        # 应用状态
        self.is_running = False
        self.start_time = None
        
    def _register_handlers(self):
        """注册MCP工具处理器"""
        
        # 注册爬取用户推文处理器
        self.mcp_server.register_tool_handler(
            "scrape_user_tweets", 
            self._handle_scrape_user_tweets
        )
        
        # 注册搜索推文处理器
        self.mcp_server.register_tool_handler(
            "scrape_keyword_tweets",
            self._handle_scrape_keyword_tweets
        )
        
        # 注册推文分析处理器
        self.mcp_server.register_tool_handler(
            "analyze_tweets",
            self._handle_analyze_tweets
        )
        
        # 注册状态查询处理器
        self.mcp_server.register_tool_handler(
            "get_scraper_status",
            self._handle_get_status
        )
        
        logger.info("已注册所有MCP工具处理器")
    
    async def initialize(self):
        """初始化应用程序"""
        try:
            logger.info("正在初始化Twitter MCP应用程序...")
            
            # 初始化Twitter爬虫
            twitter_config = self.config.twitter
            await self.twitter_scraper.initialize(
                username=twitter_config.get('username'),
                password=twitter_config.get('password'),
                cookies_path=twitter_config.get('cookies_path', 'cookies.json')
            )
            
            # 配置反屏蔽管理器
            if 'proxies' in self.config.anti_block:
                self.anti_block_manager.add_proxies_from_list(
                    self.config.anti_block['proxies']
                )
            
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("Twitter MCP应用程序初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def _handle_scrape_user_tweets(self, username: str, count: int = 20,
                                       include_replies: bool = False,
                                       max_age_days: int = 30) -> Dict[str, Any]:
        """处理爬取用户推文请求"""
        try:
            logger.info(f"开始爬取用户 @{username} 的推文")
            
            # 参数验证
            if not username:
                raise ValueError("用户名不能为空")
            
            if count <= 0 or count > 100:
                raise ValueError("推文数量必须在1-100之间")
            
            # 执行爬取
            tweets = await self.twitter_scraper.scrape_user_tweets(
                username=username,
                count=count,
                include_replies=include_replies,
                max_age_days=max_age_days
            )
            
            # 转换为字典格式
            tweets_data = [self._tweet_to_dict(tweet) for tweet in tweets]
            
            result = {
                "success": True,
                "data": {
                    "username": username,
                    "tweet_count": len(tweets_data),
                    "tweets": tweets_data,
                    "scraped_at": datetime.now().isoformat(),
                    "parameters": {
                        "count": count,
                        "include_replies": include_replies,
                        "max_age_days": max_age_days
                    }
                }
            }
            
            logger.info(f"成功爬取用户 @{username} 的 {len(tweets_data)} 条推文")
            return result
            
        except Exception as e:
            logger.error(f"爬取用户推文失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    async def _handle_scrape_keyword_tweets(self, keyword: str, count: int = 20,
                                          language: str = None,
                                          date_range: str = "30d") -> Dict[str, Any]:
        """处理搜索推文请求"""
        try:
            logger.info(f"开始搜索关键词: {keyword}")
            
            # 参数验证
            if not keyword:
                raise ValueError("关键词不能为空")
            
            if count <= 0 or count > 100:
                raise ValueError("推文数量必须在1-100之间")
            
            # 执行搜索
            tweets = await self.twitter_scraper.scrape_keyword_tweets(
                keyword=keyword,
                count=count,
                language=language,
                date_range=date_range
            )
            
            # 转换为字典格式
            tweets_data = [self._tweet_to_dict(tweet) for tweet in tweets]
            
            result = {
                "success": True,
                "data": {
                    "keyword": keyword,
                    "tweet_count": len(tweets_data),
                    "tweets": tweets_data,
                    "scraped_at": datetime.now().isoformat(),
                    "parameters": {
                        "count": count,
                        "language": language,
                        "date_range": date_range
                    }
                }
            }
            
            logger.info(f"成功搜索到关键词 '{keyword}' 的 {len(tweets_data)} 条推文")
            return result
            
        except Exception as e:
            logger.error(f"搜索推文失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    async def _handle_analyze_tweets(self, tweets: List[Dict[str, Any]],
                                   analysis_type: str = "all",
                                   language: str = "zh") -> Dict[str, Any]:
        """处理推文分析请求"""
        try:
            logger.info(f"开始分析 {len(tweets)} 条推文")
            
            # 参数验证
            if not tweets:
                raise ValueError("推文数据不能为空")
            
            if analysis_type not in ["sentiment", "summary", "topics", "all"]:
                raise ValueError("分析类型必须是: sentiment, summary, topics, all 之一")
            
            # 使用LLM处理器进行分析
            analysis_result = await self.llm_processor.analyze_tweets(
                tweets=tweets,
                analysis_type=analysis_type,
                language=language
            )
            
            if analysis_result["success"]:
                logger.info(f"成功分析 {len(tweets)} 条推文")
                return {
                    "success": True,
                    "data": {
                        "tweet_count": len(tweets),
                        "analysis_type": analysis_type,
                        "language": language,
                        "analyzed_at": datetime.now().isoformat(),
                        "results": analysis_result["data"]["analysis_result"],
                        "input_stats": analysis_result["data"]["input_stats"]
                    }
                }
            else:
                # LLM分析失败，返回模拟结果
                logger.warning(f"LLM分析失败，返回模拟结果: {analysis_result.get('error')}")
                return {
                    "success": True,
                    "data": {
                        "tweet_count": len(tweets),
                        "analysis_type": analysis_type,
                        "language": language,
                        "analyzed_at": datetime.now().isoformat(),
                        "results": {
                            "sentiment": {
                                "overall_sentiment": "neutral",
                                "positive_ratio": 0.4,
                                "negative_ratio": 0.3,
                                "neutral_ratio": 0.3,
                                "confidence": 0.5
                            } if analysis_type in ["sentiment", "all"] else None,
                            "summary": {
                                "main_topics": ["通用话题"],
                                "key_points": ["关键信息"],
                                "summary_text": "推文内容摘要（模拟结果）"
                            } if analysis_type in ["summary", "all"] else None,
                            "topics": {
                                "extracted_topics": [
                                    {"topic": "通用", "relevance": 0.6}
                                ],
                                "hashtags": [],
                                "keywords": ["关键词"]
                            } if analysis_type in ["topics", "all"] else None
                        },
                        "llm_error": analysis_result.get("error"),
                        "available_providers": analysis_result.get("available_providers", [])
                    }
                }
            
        except Exception as e:
            logger.error(f"分析推文失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    async def _handle_get_status(self) -> Dict[str, Any]:
        """处理状态查询请求"""
        try:
            scraper_status = self.twitter_scraper.get_status()
            anti_block_status = self.anti_block_manager.get_status()
            llm_status = self.llm_processor.get_status()
            
            status = {
                "success": True,
                "data": {
                    "app_status": {
                        "is_running": self.is_running,
                        "start_time": self.start_time.isoformat() if self.start_time else None,
                        "uptime_seconds": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
                    },
                    "scraper_status": scraper_status,
                    "anti_block_status": anti_block_status,
                    "llm_status": llm_status,
                    "mcp_server": {
                        "initialized": self.mcp_server.initialized,
                        "registered_tools": len(self.mcp_server.tools),
                        "registered_resources": len(self.mcp_server.resources)
                    }
                }
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _tweet_to_dict(self, tweet: TweetData) -> Dict[str, Any]:
        """将TweetData转换为字典"""
        return {
            "id": tweet.id,
            "text": tweet.text,
            "author": {
                "username": tweet.author_username,
                "display_name": tweet.author_display_name,
                "followers_count": tweet.author_followers_count,
                "verified": tweet.author_verified
            },
            "created_at": tweet.created_at,
            "metrics": {
                "retweet_count": tweet.retweet_count,
                "like_count": tweet.like_count,
                "reply_count": tweet.reply_count,
                "quote_count": tweet.quote_count
            },
            "content": {
                "hashtags": tweet.hashtags,
                "mentions": tweet.mentions,
                "urls": tweet.urls,
                "media": tweet.media
            },
            "metadata": {
                "language": tweet.language,
                "is_retweet": tweet.is_retweet,
                "is_reply": tweet.is_reply
            }
        }
    
    async def handle_mcp_message(self, message: str) -> str:
        """处理MCP消息"""
        return await self.mcp_server.handle_message(message)
    
    async def run_server(self, host: str = "127.0.0.1", port: int = 8000):
        """运行HTTP服务器（用于测试）"""
        from flask import Flask, request, jsonify
        from flask_cors import CORS
        
        app = Flask(__name__)
        CORS(app)
        
        @app.route('/mcp', methods=['POST'])
        async def handle_mcp():
            try:
                message = request.get_json()
                response = await self.handle_mcp_message(json.dumps(message))
                return jsonify(json.loads(response))
            except Exception as e:
                return jsonify({
                    "jsonrpc": "2.0",
                    "id": None,
                    "error": {"code": -32603, "message": str(e)}
                }), 500
        
        @app.route('/status', methods=['GET'])
        async def get_status():
            status = await self._handle_get_status()
            return jsonify(status)
        
        logger.info(f"启动HTTP服务器: http://{host}:{port}")
        app.run(host=host, port=port, debug=False)
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.twitter_scraper.close()
            self.anti_block_manager.cleanup()
            self.is_running = False
            logger.info("应用程序清理完成")
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")


async def main():
    """主函数"""
    app = TwitterMCPApp()
    
    try:
        # 初始化应用程序
        await app.initialize()
        
        # 测试MCP消息处理
        test_message = {
            "jsonrpc": "2.0",
            "id": "test-1",
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        response = await app.handle_mcp_message(json.dumps(test_message))
        print("MCP初始化响应:")
        print(json.dumps(json.loads(response), indent=2, ensure_ascii=False))
        
        # 测试工具列表
        list_tools_message = {
            "jsonrpc": "2.0",
            "id": "test-2",
            "method": "tools/list"
        }
        
        response = await app.handle_mcp_message(json.dumps(list_tools_message))
        print("\nMCP工具列表:")
        print(json.dumps(json.loads(response), indent=2, ensure_ascii=False))
        
        # 测试状态查询
        status = await app._handle_get_status()
        print("\n应用程序状态:")
        print(json.dumps(status, indent=2, ensure_ascii=False))
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"应用程序运行错误: {e}")
    finally:
        await app.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
