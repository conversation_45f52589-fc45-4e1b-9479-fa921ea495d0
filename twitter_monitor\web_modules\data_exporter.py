"""
数据导出模块
提供多种格式的数据导出功能
"""

import streamlit as st
import pandas as pd
import json
import csv
import io
import base64
from datetime import datetime
from typing import List, Dict, Any
import zipfile
import tempfile
import os


class DataExporter:
    """数据导出器"""
    
    def __init__(self):
        pass
    
    def render(self, results: List[Dict[str, Any]]):
        """渲染数据导出界面"""
        st.markdown('<h1 class="main-header">📤 数据导出</h1>', unsafe_allow_html=True)
        
        if not results:
            st.info("暂无数据可导出")
            return
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📋 快速导出", "📊 自定义导出", "📈 报告生成", "⚙️ 导出设置"])
        
        with tab1:
            self.render_quick_export(results)
        
        with tab2:
            self.render_custom_export(results)
        
        with tab3:
            self.render_report_generation(results)
        
        with tab4:
            self.render_export_settings()
    
    def render_quick_export(self, results: List[Dict[str, Any]]):
        """渲染快速导出"""
        st.markdown("### 📋 快速导出")
        st.markdown("一键导出常用格式的数据文件")
        
        # 数据统计
        total_results = len(results)
        total_tweets = sum(len(result.get('tweets', [])) for result in results)
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("监测记录", total_results)
        with col2:
            st.metric("推文总数", total_tweets)
        with col3:
            st.metric("数据大小", f"{self.estimate_data_size(results):.1f} MB")
        
        st.markdown("---")
        
        # 快速导出按钮
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("📄 导出JSON", use_container_width=True):
                self.export_json(results, "twitter_monitor_data.json")
        
        with col2:
            if st.button("📊 导出CSV", use_container_width=True):
                self.export_csv(results, "twitter_monitor_data.csv")
        
        with col3:
            if st.button("📈 导出Excel", use_container_width=True):
                self.export_excel(results, "twitter_monitor_data.xlsx")
        
        with col4:
            if st.button("📦 导出全部", use_container_width=True):
                self.export_all_formats(results)
        
        # 最近导出记录
        self.render_export_history()
    
    def render_custom_export(self, results: List[Dict[str, Any]]):
        """渲染自定义导出"""
        st.markdown("### 📊 自定义导出")
        st.markdown("根据需要选择导出的数据和格式")
        
        # 数据筛选选项
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 📅 时间范围")
            date_range = st.date_input(
                "选择日期范围",
                value=[],
                help="选择要导出的数据时间范围"
            )
            
            st.markdown("#### 📱 监测类型")
            monitor_types = st.multiselect(
                "选择监测类型",
                ["account", "keyword"],
                default=["account", "keyword"],
                format_func=lambda x: "账号监测" if x == "account" else "关键词监测"
            )
        
        with col2:
            st.markdown("#### 🎯 监测目标")
            all_targets = list(set(result.get('target', '') for result in results))
            selected_targets = st.multiselect(
                "选择监测目标",
                all_targets,
                default=all_targets[:5] if len(all_targets) > 5 else all_targets
            )
            
            st.markdown("#### 📊 数据字段")
            available_fields = [
                "推文内容", "作者信息", "发布时间", "互动数据", 
                "标签信息", "提及信息", "监测信息"
            ]
            selected_fields = st.multiselect(
                "选择导出字段",
                available_fields,
                default=available_fields
            )
        
        # 应用筛选
        filtered_results = self.filter_results(results, date_range, monitor_types, selected_targets)
        
        st.markdown(f"**筛选结果**: {len(filtered_results)} 条记录")
        
        # 导出格式选择
        st.markdown("#### 📤 导出格式")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            export_format = st.selectbox(
                "文件格式",
                ["JSON", "CSV", "Excel", "TXT"],
                index=0
            )
        
        with col2:
            include_metadata = st.checkbox("包含元数据", value=True)
        
        with col3:
            compress_file = st.checkbox("压缩文件", value=False)
        
        # 自定义导出按钮
        if st.button("🚀 开始自定义导出", use_container_width=True):
            if filtered_results:
                self.custom_export(
                    filtered_results, export_format, selected_fields, 
                    include_metadata, compress_file
                )
            else:
                st.warning("没有符合条件的数据可导出")
    
    def render_report_generation(self, results: List[Dict[str, Any]]):
        """渲染报告生成"""
        st.markdown("### 📈 监测报告生成")
        st.markdown("生成包含统计分析的详细报告")
        
        # 报告类型选择
        col1, col2 = st.columns(2)
        
        with col1:
            report_type = st.selectbox(
                "报告类型",
                ["综合报告", "账号分析报告", "关键词分析报告", "趋势分析报告"],
                index=0
            )
        
        with col2:
            report_format = st.selectbox(
                "报告格式",
                ["HTML", "PDF", "Word", "PowerPoint"],
                index=0
            )
        
        # 报告配置
        st.markdown("#### ⚙️ 报告配置")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            include_charts = st.checkbox("包含图表", value=True)
        
        with col2:
            include_raw_data = st.checkbox("包含原始数据", value=False)
        
        with col3:
            include_summary = st.checkbox("包含执行摘要", value=True)
        
        # 报告预览
        if st.button("👀 预览报告"):
            self.preview_report(results, report_type)
        
        # 生成报告
        if st.button("📋 生成报告", use_container_width=True):
            self.generate_report(
                results, report_type, report_format,
                include_charts, include_raw_data, include_summary
            )
    
    def render_export_settings(self):
        """渲染导出设置"""
        st.markdown("### ⚙️ 导出设置")
        
        # 文件命名设置
        st.markdown("#### 📝 文件命名")
        
        col1, col2 = st.columns(2)
        
        with col1:
            filename_template = st.text_input(
                "文件名模板",
                value="twitter_monitor_{date}_{type}",
                help="可用变量: {date}, {time}, {type}, {count}"
            )
        
        with col2:
            date_format = st.selectbox(
                "日期格式",
                ["%Y%m%d", "%Y-%m-%d", "%Y%m%d_%H%M%S"],
                index=0
            )
        
        # 数据格式设置
        st.markdown("#### 📊 数据格式")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            csv_encoding = st.selectbox(
                "CSV编码",
                ["utf-8", "gbk", "utf-8-sig"],
                index=0
            )
        
        with col2:
            csv_separator = st.selectbox(
                "CSV分隔符",
                [",", ";", "\t"],
                index=0,
                format_func=lambda x: "逗号" if x == "," else ("分号" if x == ";" else "制表符")
            )
        
        with col3:
            json_indent = st.number_input(
                "JSON缩进",
                min_value=0,
                max_value=8,
                value=2
            )
        
        # 自动导出设置
        st.markdown("#### 🔄 自动导出")
        
        col1, col2 = st.columns(2)
        
        with col1:
            auto_export = st.checkbox("启用自动导出", value=False)
        
        with col2:
            if auto_export:
                auto_export_interval = st.selectbox(
                    "导出间隔",
                    ["每小时", "每天", "每周", "每月"],
                    index=1
                )
        
        # 保存设置
        if st.button("💾 保存设置"):
            self.save_export_settings({
                'filename_template': filename_template,
                'date_format': date_format,
                'csv_encoding': csv_encoding,
                'csv_separator': csv_separator,
                'json_indent': json_indent,
                'auto_export': auto_export
            })
    
    def export_json(self, results: List[Dict[str, Any]], filename: str):
        """导出JSON格式"""
        try:
            # 准备数据
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_results': len(results),
                    'total_tweets': sum(len(result.get('tweets', [])) for result in results),
                    'format': 'JSON'
                },
                'results': results
            }
            
            # 转换为JSON字符串
            json_str = json.dumps(export_data, ensure_ascii=False, indent=2, default=str)
            
            # 创建下载链接
            b64 = base64.b64encode(json_str.encode('utf-8')).decode()
            href = f'<a href="data:application/json;base64,{b64}" download="{filename}">📄 下载 {filename}</a>'
            
            st.markdown(href, unsafe_allow_html=True)
            st.success(f"✅ JSON文件已准备就绪: {filename}")
            
        except Exception as e:
            st.error(f"❌ JSON导出失败: {e}")
    
    def export_csv(self, results: List[Dict[str, Any]], filename: str):
        """导出CSV格式"""
        try:
            # 扁平化数据
            flattened_data = []
            for result in results:
                tweets = result.get('tweets', [])
                for tweet in tweets:
                    row = {
                        'monitor_type': result.get('monitor_type', ''),
                        'target': result.get('target', ''),
                        'timestamp': result.get('timestamp', ''),
                        'tweet_id': tweet.get('id', ''),
                        'text': tweet.get('text', ''),
                        'author_username': tweet.get('author_username', ''),
                        'author_display_name': tweet.get('author_display_name', ''),
                        'created_at': tweet.get('created_at', ''),
                        'like_count': tweet.get('like_count', 0),
                        'retweet_count': tweet.get('retweet_count', 0),
                        'reply_count': tweet.get('reply_count', 0),
                        'hashtags': ', '.join(tweet.get('hashtags', [])),
                        'mentions': ', '.join(tweet.get('mentions', [])),
                        'is_retweet': tweet.get('is_retweet', False),
                        'is_reply': tweet.get('is_reply', False)
                    }
                    flattened_data.append(row)
            
            # 创建DataFrame
            df = pd.DataFrame(flattened_data)
            
            # 转换为CSV
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False, encoding='utf-8')
            csv_str = csv_buffer.getvalue()
            
            # 创建下载链接
            b64 = base64.b64encode(csv_str.encode('utf-8')).decode()
            href = f'<a href="data:text/csv;base64,{b64}" download="{filename}">📊 下载 {filename}</a>'
            
            st.markdown(href, unsafe_allow_html=True)
            st.success(f"✅ CSV文件已准备就绪: {filename}")
            
        except Exception as e:
            st.error(f"❌ CSV导出失败: {e}")
    
    def export_excel(self, results: List[Dict[str, Any]], filename: str):
        """导出Excel格式"""
        try:
            # 创建Excel文件
            excel_buffer = io.BytesIO()
            
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                # 监测结果汇总表
                summary_data = []
                for result in results:
                    summary_data.append({
                        '监测类型': result.get('monitor_type', ''),
                        '监测目标': result.get('target', ''),
                        '监测时间': result.get('timestamp', ''),
                        '推文数量': len(result.get('tweets', [])),
                        '监测状态': '成功' if result.get('success', False) else '失败',
                        '错误信息': result.get('error_message', '')
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='监测汇总', index=False)
                
                # 推文详情表
                tweet_data = []
                for result in results:
                    tweets = result.get('tweets', [])
                    for tweet in tweets:
                        tweet_data.append({
                            '监测类型': result.get('monitor_type', ''),
                            '监测目标': result.get('target', ''),
                            '推文ID': tweet.get('id', ''),
                            '推文内容': tweet.get('text', ''),
                            '作者用户名': tweet.get('author_username', ''),
                            '作者显示名': tweet.get('author_display_name', ''),
                            '发布时间': tweet.get('created_at', ''),
                            '点赞数': tweet.get('like_count', 0),
                            '转发数': tweet.get('retweet_count', 0),
                            '回复数': tweet.get('reply_count', 0),
                            '标签': ', '.join(tweet.get('hashtags', [])),
                            '提及': ', '.join(tweet.get('mentions', [])),
                            '是否转发': tweet.get('is_retweet', False),
                            '是否回复': tweet.get('is_reply', False)
                        })
                
                if tweet_data:
                    tweet_df = pd.DataFrame(tweet_data)
                    tweet_df.to_excel(writer, sheet_name='推文详情', index=False)
            
            # 创建下载链接
            excel_buffer.seek(0)
            b64 = base64.b64encode(excel_buffer.read()).decode()
            href = f'<a href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64}" download="{filename}">📈 下载 {filename}</a>'
            
            st.markdown(href, unsafe_allow_html=True)
            st.success(f"✅ Excel文件已准备就绪: {filename}")
            
        except Exception as e:
            st.error(f"❌ Excel导出失败: {e}")
    
    def export_all_formats(self, results: List[Dict[str, Any]]):
        """导出所有格式"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                # 导出各种格式到临时目录
                files_created = []
                
                # JSON
                json_file = os.path.join(temp_dir, f"twitter_data_{timestamp}.json")
                self.save_json_file(results, json_file)
                files_created.append(json_file)
                
                # CSV
                csv_file = os.path.join(temp_dir, f"twitter_data_{timestamp}.csv")
                self.save_csv_file(results, csv_file)
                files_created.append(csv_file)
                
                # Excel
                excel_file = os.path.join(temp_dir, f"twitter_data_{timestamp}.xlsx")
                self.save_excel_file(results, excel_file)
                files_created.append(excel_file)
                
                # 创建ZIP文件
                zip_buffer = io.BytesIO()
                with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for file_path in files_created:
                        zip_file.write(file_path, os.path.basename(file_path))
                
                # 创建下载链接
                zip_buffer.seek(0)
                b64 = base64.b64encode(zip_buffer.read()).decode()
                href = f'<a href="data:application/zip;base64,{b64}" download="twitter_monitor_data_{timestamp}.zip">📦 下载完整数据包</a>'
                
                st.markdown(href, unsafe_allow_html=True)
                st.success("✅ 完整数据包已准备就绪")
                
        except Exception as e:
            st.error(f"❌ 批量导出失败: {e}")
    
    def filter_results(self, results: List[Dict[str, Any]], date_range, 
                      monitor_types: List[str], selected_targets: List[str]) -> List[Dict[str, Any]]:
        """筛选结果"""
        filtered = []
        
        for result in results:
            # 监测类型筛选
            if result.get('monitor_type') not in monitor_types:
                continue
            
            # 目标筛选
            if result.get('target') not in selected_targets:
                continue
            
            # 日期筛选
            if date_range and len(date_range) == 2:
                try:
                    result_date = datetime.fromisoformat(result.get('timestamp', '').replace('Z', '+00:00')).date()
                    if not (date_range[0] <= result_date <= date_range[1]):
                        continue
                except:
                    continue
            
            filtered.append(result)
        
        return filtered
    
    def estimate_data_size(self, results: List[Dict[str, Any]]) -> float:
        """估算数据大小（MB）"""
        try:
            json_str = json.dumps(results, ensure_ascii=False, default=str)
            size_bytes = len(json_str.encode('utf-8'))
            return size_bytes / (1024 * 1024)
        except:
            return 0.0
    
    def render_export_history(self):
        """渲染导出历史"""
        st.markdown("#### 📋 最近导出")
        
        # 这里可以从session state或数据库获取导出历史
        # 暂时显示示例数据
        history_data = [
            {"时间": "2024-01-01 10:30", "格式": "JSON", "大小": "2.3 MB", "状态": "成功"},
            {"时间": "2024-01-01 09:15", "格式": "CSV", "大小": "1.8 MB", "状态": "成功"},
            {"时间": "2024-01-01 08:45", "格式": "Excel", "大小": "3.1 MB", "状态": "成功"}
        ]
        
        if history_data:
            df = pd.DataFrame(history_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无导出历史")
    
    def save_json_file(self, results: List[Dict[str, Any]], filepath: str):
        """保存JSON文件到本地"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    def save_csv_file(self, results: List[Dict[str, Any]], filepath: str):
        """保存CSV文件到本地"""
        # 实现CSV保存逻辑
        pass
    
    def save_excel_file(self, results: List[Dict[str, Any]], filepath: str):
        """保存Excel文件到本地"""
        # 实现Excel保存逻辑
        pass
    
    def custom_export(self, results: List[Dict[str, Any]], format_type: str, 
                     fields: List[str], include_metadata: bool, compress: bool):
        """自定义导出"""
        st.info(f"正在导出 {format_type} 格式...")
        # 实现自定义导出逻辑
    
    def generate_report(self, results: List[Dict[str, Any]], report_type: str, 
                       format_type: str, include_charts: bool, include_raw_data: bool, 
                       include_summary: bool):
        """生成报告"""
        st.info(f"正在生成 {report_type} ({format_type} 格式)...")
        # 实现报告生成逻辑
    
    def preview_report(self, results: List[Dict[str, Any]], report_type: str):
        """预览报告"""
        st.markdown("#### 📋 报告预览")
        st.info("报告预览功能开发中...")
    
    def save_export_settings(self, settings: Dict[str, Any]):
        """保存导出设置"""
        st.success("✅ 导出设置已保存")
    
    def generate_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成摘要报告"""
        total_results = len(results)
        total_tweets = sum(len(result.get('tweets', [])) for result in results)
        
        return {
            'summary': {
                'total_results': total_results,
                'total_tweets': total_tweets,
                'success_rate': sum(1 for r in results if r.get('success', False)) / total_results * 100 if total_results > 0 else 0,
                'export_time': datetime.now().isoformat()
            },
            'results': results
        }
