#!/usr/bin/env python3
"""
Twitter账号监测脚本主程序
"""

import sys
import signal
import argparse
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from core import TwitterMonitor
from utils import setup_logger, export_tweets_to_csv, export_to_json
from utils.helpers import format_tweet_text, generate_report_filename


class TwitterMonitorApp:
    """Twitter监测应用程序"""
    
    def __init__(self, config_path: str = None):
        # 加载配置
        self.config = Config(config_path)
        
        # 设置日志
        self.logger = setup_logger('twitter_monitor', self.config.logging_config)
        
        # 初始化监测器
        self.monitor = TwitterMonitor({
            'accounts': self.config.get_accounts(),
            'keywords': self.config.get_keywords(),
            'check_interval': self.config.get('monitor.check_interval', 300),
            'max_tweets': self.config.get('monitor.max_tweets', 20),
            'data_dir': self.config.get('monitor.data_dir', 'data'),
            'scraper': self.config.scraper,
            'anti_block': self.config.anti_block
        })
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 添加结果回调
        self.monitor.add_result_callback(self._handle_monitor_result)
        
        self.logger.info("Twitter监测应用程序初始化完成")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在关闭应用程序...")
        self.stop()
        sys.exit(0)
    
    def _handle_monitor_result(self, result):
        """处理监测结果"""
        try:
            # 如果配置了实时打印，显示新推文
            if self.config.get('output.print_new_tweets', True) and result.success and result.tweets:
                self._print_new_tweets(result)
            
            # 导出数据
            if result.success and result.tweets:
                self._export_result_data(result)
                
        except Exception as e:
            self.logger.error(f"处理监测结果失败: {e}")
    
    def _print_new_tweets(self, result):
        """打印新推文"""
        print(f"\n🔔 发现新推文 - {result.monitor_type.upper()}: {result.target}")
        print(f"📅 时间: {result.timestamp}")
        print(f"📊 数量: {len(result.tweets)} 条")
        print("=" * 80)
        
        max_display = 3  # 最多显示3条
        max_text_length = self.config.get('output.max_text_length', 200)
        
        for i, tweet in enumerate(result.tweets[:max_display], 1):
            print(f"\n{i}. @{tweet.author_username} ({tweet.author_display_name})")
            print(f"   🕒 {tweet.created_at}")
            
            # 格式化推文文本
            formatted_text = format_tweet_text(tweet.text, max_text_length)
            print(f"   💬 {formatted_text}")
            
            # 显示互动数据
            print(f"   📈 ❤️{tweet.like_count} 🔄{tweet.retweet_count} 💬{tweet.reply_count}")
            
            # 显示标签和提及
            if tweet.hashtags:
                print(f"   🏷️ {' '.join(['#' + tag for tag in tweet.hashtags])}")
            if tweet.mentions:
                print(f"   👥 {' '.join(['@' + mention for mention in tweet.mentions])}")
        
        if len(result.tweets) > max_display:
            print(f"\n... 还有 {len(result.tweets) - max_display} 条推文")
        
        print("=" * 80)
    
    def _export_result_data(self, result):
        """导出结果数据"""
        try:
            if not result.tweets:
                return
            
            # 生成文件名
            base_filename = generate_report_filename(result.monitor_type, result.target)
            
            # 导出JSON
            if self.config.get('output.save_json', True):
                json_filename = f"{base_filename}.json"
                json_filepath = Path(self.config.get('monitor.data_dir', 'data')) / 'exports' / json_filename
                
                export_data = {
                    'monitor_type': result.monitor_type,
                    'target': result.target,
                    'timestamp': result.timestamp,
                    'tweet_count': len(result.tweets),
                    'tweets': [tweet.__dict__ for tweet in result.tweets]
                }
                
                if export_to_json([export_data], str(json_filepath)):
                    self.logger.info(f"数据已导出到JSON: {json_filepath}")
            
            # 导出CSV
            if self.config.get('output.save_csv', False):
                csv_filename = f"{base_filename}.csv"
                csv_filepath = Path(self.config.get('monitor.data_dir', 'data')) / 'exports' / csv_filename
                
                tweet_dicts = []
                for tweet in result.tweets:
                    tweet_dict = tweet.__dict__.copy()
                    tweet_dict['monitor_type'] = result.monitor_type
                    tweet_dict['monitor_target'] = result.target
                    tweet_dicts.append(tweet_dict)
                
                if export_tweets_to_csv(tweet_dicts, str(csv_filepath)):
                    self.logger.info(f"数据已导出到CSV: {csv_filepath}")
                    
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
    
    def start(self):
        """启动监测"""
        try:
            self.logger.info("正在启动Twitter监测...")
            
            # 显示配置信息
            self._print_startup_info()
            
            # 初始化并启动监测器
            if self.monitor.initialize():
                self.monitor.start_monitoring()
                self.logger.info("Twitter监测已启动，按Ctrl+C停止")
                
                # 保持程序运行
                try:
                    while self.monitor.is_running:
                        import time
                        time.sleep(1)
                except KeyboardInterrupt:
                    self.logger.info("收到中断信号")
            else:
                self.logger.error("监测器初始化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"启动监测失败: {e}")
            return False
        
        return True
    
    def stop(self):
        """停止监测"""
        try:
            self.logger.info("正在停止Twitter监测...")
            self.monitor.cleanup()
            self.logger.info("Twitter监测已停止")
        except Exception as e:
            self.logger.error(f"停止监测时发生错误: {e}")
    
    def _print_startup_info(self):
        """打印启动信息"""
        print("\n" + "=" * 60)
        print("🐦 Twitter账号监测脚本")
        print("=" * 60)
        
        accounts = self.config.get_accounts()
        keywords = self.config.get_keywords()
        
        if accounts:
            print(f"📱 监测账号 ({len(accounts)}个):")
            for account in accounts:
                print(f"   • @{account}")
        
        if keywords:
            print(f"🔍 监测关键词 ({len(keywords)}个):")
            for keyword in keywords:
                print(f"   • {keyword}")
        
        print(f"⏱️ 检查间隔: {self.config.get('monitor.check_interval', 300)}秒")
        print(f"📊 每次最大推文数: {self.config.get('monitor.max_tweets', 20)}")
        print(f"💾 数据目录: {self.config.get('monitor.data_dir', 'data')}")
        
        # 反屏蔽配置
        anti_block = self.config.anti_block
        if anti_block.get('proxies'):
            print(f"🔒 代理数量: {len(anti_block['proxies'])}")
        
        print("=" * 60)
        print("🚀 启动中...")
    
    def add_account(self, account: str):
        """添加监测账号"""
        self.config.add_account(account)
        self.config.save_config()
        self.logger.info(f"已添加监测账号: @{account}")
    
    def remove_account(self, account: str):
        """移除监测账号"""
        self.config.remove_account(account)
        self.config.save_config()
        self.logger.info(f"已移除监测账号: @{account}")
    
    def add_keyword(self, keyword: str):
        """添加监测关键词"""
        self.config.add_keyword(keyword)
        self.config.save_config()
        self.logger.info(f"已添加监测关键词: {keyword}")
    
    def remove_keyword(self, keyword: str):
        """移除监测关键词"""
        self.config.remove_keyword(keyword)
        self.config.save_config()
        self.logger.info(f"已移除监测关键词: {keyword}")
    
    def get_status(self):
        """获取状态信息"""
        return self.monitor.get_status()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Twitter账号监测脚本')
    parser.add_argument('--config', '-c', help='配置文件路径', default='config/config.yaml')
    parser.add_argument('--add-account', help='添加监测账号')
    parser.add_argument('--remove-account', help='移除监测账号')
    parser.add_argument('--add-keyword', help='添加监测关键词')
    parser.add_argument('--remove-keyword', help='移除监测关键词')
    parser.add_argument('--status', action='store_true', help='显示状态信息')
    parser.add_argument('--test', action='store_true', help='测试模式（不启动监测）')
    
    args = parser.parse_args()
    
    try:
        # 创建应用程序实例
        app = TwitterMonitorApp(args.config)
        
        # 处理命令行参数
        if args.add_account:
            app.add_account(args.add_account)
            print(f"✅ 已添加监测账号: @{args.add_account}")
            return
        
        if args.remove_account:
            app.remove_account(args.remove_account)
            print(f"✅ 已移除监测账号: @{args.remove_account}")
            return
        
        if args.add_keyword:
            app.add_keyword(args.add_keyword)
            print(f"✅ 已添加监测关键词: {args.add_keyword}")
            return
        
        if args.remove_keyword:
            app.remove_keyword(args.remove_keyword)
            print(f"✅ 已移除监测关键词: {args.remove_keyword}")
            return
        
        if args.status:
            status = app.get_status()
            print("📊 监测器状态:")
            print(f"   运行状态: {'运行中' if status['is_running'] else '已停止'}")
            print(f"   监测账号: {len(status['accounts'])}个")
            print(f"   监测关键词: {len(status['keywords'])}个")
            print(f"   成功次数: {status['success_count']}")
            print(f"   错误次数: {status['error_count']}")
            return
        
        if args.test:
            print("🧪 测试模式 - 配置加载成功")
            print(f"监测账号: {app.config.get_accounts()}")
            print(f"监测关键词: {app.config.get_keywords()}")
            return
        
        # 启动监测
        app.start()
        
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
