# Twitter监测系统 - Web界面使用说明

## 🌟 功能概览

Twitter监测系统Web界面是一个功能完整的可视化管理平台，提供以下核心功能：

- **🏠 监控面板**: 实时监测状态和数据概览
- **⚙️ 监测配置**: 账号、关键词、频率等参数设置
- **📊 结果展示**: 推文列表、搜索过滤、详细信息
- **📤 数据导出**: JSON、CSV、Excel等格式导出
- **🔔 钉钉推送**: 实时推文推送和自动通知
- **📈 统计分析**: 趋势分析、用户分析、内容分析

## 🚀 快速开始

### 1. 启动Web界面

```bash
# 方法一：使用启动器（推荐）
python start_web.py

# 方法二：直接启动
streamlit run web_app.py
```

### 2. 访问界面

- 默认地址: http://localhost:8501
- 界面会自动在浏览器中打开
- 支持Chrome、Firefox、Safari等主流浏览器

### 3. 基本配置

1. 点击侧边栏的 **⚙️ 监测配置**
2. 在 **📱 账号管理** 中添加要监测的Twitter账号
3. 在 **🔍 关键词管理** 中添加要监测的关键词
4. 在 **⏱️ 监测设置** 中配置检查频率

## 📋 详细功能说明

### 🏠 监控面板

**主要功能:**
- 实时显示监测状态和关键指标
- 监测活动趋势图表
- 最新推文实时展示
- 系统状态监控

**操作说明:**
- 使用侧边栏的 **▶️ 开始** / **⏹️ 停止** 按钮控制监测
- 点击 **🔄 刷新数据** 获取最新结果
- 点击 **📋 导出报告** 快速导出数据

### ⚙️ 监测配置

#### 📱 账号管理
- **添加账号**: 输入Twitter用户名（不含@符号）
- **批量添加**: 每行一个账号名，支持批量导入
- **删除账号**: 点击账号旁的 🗑️ 按钮

#### 🔍 关键词管理
- **添加关键词**: 支持中英文关键词
- **批量添加**: 每行一个关键词
- **删除关键词**: 点击关键词旁的 🗑️ 按钮

#### ⏱️ 监测设置
- **检查间隔**: 建议设置为300秒（5分钟）以上
- **最大推文数**: 每次监测获取的推文数量
- **数据目录**: 监测数据的存储位置

#### 🛡️ 反屏蔽配置
- **请求频率控制**: 设置每分钟请求数和突发限制
- **代理配置**: 支持HTTP/HTTPS/SOCKS代理
- **批量代理**: 支持代理池轮换

### 📊 结果展示

#### 📝 推文列表
- **排序功能**: 按时间、点赞数、转发数等排序
- **筛选功能**: 按监测类型筛选
- **分页显示**: 支持大量数据的分页浏览
- **推文详情**: 显示完整推文信息和互动数据

#### 📈 数据统计
- **基本统计**: 监测次数、推文总数、成功率等
- **时间趋势**: 监测活动的时间趋势图
- **类型分布**: 监测类型和目标的分布图
- **互动分析**: 推文互动数据的统计分析

#### 🔍 搜索过滤
- **内容搜索**: 在推文内容中搜索关键词
- **作者筛选**: 按推文作者筛选
- **日期筛选**: 按发布日期筛选
- **互动筛选**: 按点赞数、转发数筛选

### 📤 数据导出

#### 📋 快速导出
- **JSON格式**: 完整的结构化数据
- **CSV格式**: 表格格式，便于Excel打开
- **Excel格式**: 多工作表，包含汇总和详情
- **完整数据包**: ZIP压缩包，包含所有格式

#### 📊 自定义导出
- **时间范围**: 选择导出的时间范围
- **监测类型**: 选择账号监测或关键词监测
- **数据字段**: 选择要导出的字段
- **文件格式**: 选择导出格式和压缩选项

#### 📈 报告生成
- **综合报告**: 包含所有监测数据的综合分析
- **账号分析报告**: 专注于账号监测的分析
- **关键词分析报告**: 专注于关键词监测的分析
- **趋势分析报告**: 时间趋势和变化分析

### 🔔 钉钉推送

#### ⚙️ 机器人配置
1. 在钉钉群中添加自定义机器人
2. 选择"加签"安全设置
3. 复制Webhook地址和加签密钥
4. 在Web界面中配置并启用

#### 📤 手动推送
- **文本消息**: 发送纯文本消息
- **Markdown消息**: 支持格式化的消息
- **链接消息**: 发送带链接的消息
- **推文消息**: 直接推送监测到的推文

#### 🔄 自动推送
- **推送规则**: 设置自动推送的触发条件
- **频率限制**: 控制推送频率，避免刷屏
- **消息模板**: 自定义推送消息的格式

### 📈 统计分析

#### 📊 总体概览
- **关键指标**: 监测次数、推文数、成功率等
- **类型分布**: 饼图显示监测类型分布
- **时间热力图**: 显示监测活动的时间分布
- **热门目标**: 最活跃的监测目标排行

#### 📈 趋势分析
- **推文趋势**: 推文数量的时间趋势
- **互动趋势**: 点赞、转发、回复的趋势
- **效率趋势**: 监测成功率和效率分析

#### 👥 用户分析
- **活跃用户**: 推文数量和互动数据排行
- **用户互动**: 用户互动数据的散点图分析
- **活跃时间**: 用户发推的时间分布

#### 🔍 内容分析
- **词频分析**: 高频词汇统计
- **标签分析**: 热门标签统计
- **推文长度**: 推文长度分布分析
- **情感分析**: 简化的情感倾向分析

## 💡 使用技巧

### 🎯 监测策略

1. **合理设置频率**: 
   - 建议检查间隔≥300秒，避免被限制
   - 热门账号可适当增加频率

2. **关键词选择**:
   - 使用具体的关键词，避免过于宽泛
   - 结合中英文关键词提高覆盖率

3. **代理使用**:
   - 配置代理池提高稳定性
   - 定期更换代理避免封禁

### 📊 数据分析

1. **趋势观察**:
   - 关注推文数量的异常变化
   - 分析互动数据的趋势

2. **内容洞察**:
   - 通过词频分析发现热点话题
   - 关注高互动推文的特征

3. **时间规律**:
   - 分析用户活跃时间规律
   - 优化监测时间安排

### 🔔 推送优化

1. **消息模板**:
   - 自定义推送消息格式
   - 包含关键信息和链接

2. **频率控制**:
   - 设置合理的推送频率
   - 避免重要消息被忽略

## ⚠️ 注意事项

### 🔒 使用规范

1. **遵守法律法规**: 仅监测公开内容，遵守相关法律
2. **尊重服务条款**: 遵守Twitter服务条款
3. **合理使用**: 控制监测频率，避免过度请求
4. **数据安全**: 妥善保管配置信息和监测数据

### 🛠️ 故障排除

1. **监测失败**: 检查网络连接和Chrome浏览器
2. **推送失败**: 验证钉钉机器人配置
3. **数据异常**: 检查配置参数和日志文件
4. **性能问题**: 适当减少监测目标和频率

### 📞 技术支持

如遇到问题，请：
1. 查看日志文件：`logs/twitter_monitor.log`
2. 检查配置文件：`config/config.yaml`
3. 重启Web应用：`python start_web.py`

---

**版本**: 1.0.0  
**更新时间**: 2024年12月  
**技术支持**: 查看README.md获取更多信息
