#!/usr/bin/env python3
"""
Twitter监测系统Web界面启动器
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'streamlit',
        'plotly',
        'pandas',
        'numpy',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    return missing_packages


def install_web_dependencies():
    """安装Web界面依赖"""
    print("📦 正在安装Web界面依赖包...")
    
    web_packages = [
        'streamlit>=1.28.0',
        'plotly>=5.17.0',
        'openpyxl>=3.1.0'
    ]
    
    try:
        for package in web_packages:
            print(f"  安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"  ✅ {package} 安装成功")
            else:
                print(f"  ❌ {package} 安装失败: {result.stderr}")
                return False
        
        print("✅ Web界面依赖包安装完成")
        return True
        
    except Exception as e:
        print(f"❌ 安装依赖包时发生错误: {e}")
        return False


def create_streamlit_config():
    """创建Streamlit配置文件"""
    config_dir = Path.home() / ".streamlit"
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "config.toml"
    
    config_content = """
[server]
port = 8501
headless = true
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#1DA1F2"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
"""
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        print(f"✅ Streamlit配置文件已创建: {config_file}")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False


def start_web_app():
    """启动Web应用"""
    print("🚀 正在启动Twitter监测系统Web界面...")
    
    # 获取当前脚本目录
    current_dir = Path(__file__).parent
    web_app_file = current_dir / "web_app.py"
    
    if not web_app_file.exists():
        print(f"❌ 找不到Web应用文件: {web_app_file}")
        return False
    
    try:
        # 启动Streamlit应用
        cmd = [sys.executable, "-m", "streamlit", "run", str(web_app_file)]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("=" * 60)
        print("🌐 Web界面将在浏览器中自动打开")
        print("📍 默认地址: http://localhost:8501")
        print("⏹️ 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动应用
        subprocess.run(cmd, cwd=current_dir)
        
    except KeyboardInterrupt:
        print("\n👋 Web应用已停止")
    except Exception as e:
        print(f"❌ 启动Web应用失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🐦 Twitter监测系统 - Web界面启动器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return
    
    print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # 检查依赖包
    print("\n🔍 检查依赖包...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n📦 发现缺少依赖包: {', '.join(missing_packages)}")
        
        # 询问是否自动安装
        try:
            response = input("是否自动安装缺少的依赖包? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                if not install_web_dependencies():
                    print("❌ 依赖包安装失败，请手动安装")
                    print("手动安装命令:")
                    print("pip install streamlit plotly openpyxl")
                    return
            else:
                print("❌ 请手动安装依赖包后重试")
                return
        except KeyboardInterrupt:
            print("\n👋 安装被用户取消")
            return
    
    # 创建Streamlit配置
    print("\n⚙️ 配置Streamlit...")
    create_streamlit_config()
    
    # 启动Web应用
    print("\n🚀 启动Web应用...")
    start_web_app()


if __name__ == "__main__":
    main()
