#!/usr/bin/env python3
"""
Twitter监测脚本演示程序
展示基本功能的使用方法
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from core import TwitterScraper, TwitterMonitor
from utils import setup_logger


def demo_basic_scraping():
    """演示基本爬取功能"""
    print("🐦 演示基本爬取功能")
    print("=" * 50)
    
    try:
        # 创建爬虫实例
        scraper = TwitterScraper({
            'request_delay': [2, 4],
            'max_retries': 2,
            'timeout': 10
        })
        
        print("📱 正在初始化爬虫...")
        scraper.initialize(use_selenium=True)
        
        # 演示爬取用户推文
        print("\n🔍 演示爬取用户推文...")
        print("目标用户: @elonmusk")
        print("推文数量: 3条")
        
        tweets = scraper.scrape_user_tweets("elonmusk", count=3)
        
        if tweets:
            print(f"\n✅ 成功爬取 {len(tweets)} 条推文:")
            for i, tweet in enumerate(tweets, 1):
                print(f"\n{i}. @{tweet.author_username}")
                print(f"   时间: {tweet.created_at}")
                print(f"   内容: {tweet.text[:100]}...")
                print(f"   互动: ❤️{tweet.like_count} 🔄{tweet.retweet_count}")
        else:
            print("❌ 未能获取到推文")
        
        # 演示关键词搜索
        print("\n🔍 演示关键词搜索...")
        print("关键词: AI")
        print("推文数量: 3条")
        
        search_tweets = scraper.search_tweets("AI", count=3)
        
        if search_tweets:
            print(f"\n✅ 成功搜索到 {len(search_tweets)} 条推文:")
            for i, tweet in enumerate(search_tweets, 1):
                print(f"\n{i}. @{tweet.author_username}")
                print(f"   时间: {tweet.created_at}")
                print(f"   内容: {tweet.text[:100]}...")
                print(f"   互动: ❤️{tweet.like_count} 🔄{tweet.retweet_count}")
        else:
            print("❌ 未能搜索到推文")
        
        scraper.close()
        print("\n✅ 演示完成")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")


def demo_monitoring():
    """演示监测功能"""
    print("\n🔔 演示监测功能")
    print("=" * 50)
    
    try:
        # 创建监测配置
        config = {
            'accounts': ['elonmusk'],
            'keywords': ['AI'],
            'check_interval': 30,  # 30秒检查一次（演示用）
            'max_tweets': 3,
            'data_dir': 'demo_data',
            'scraper': {
                'request_delay': [2, 4],
                'max_retries': 2
            },
            'anti_block': {
                'requests_per_minute': 60,
                'burst_limit': 10
            }
        }
        
        # 创建监测器
        monitor = TwitterMonitor(config)
        
        # 添加结果回调
        def demo_callback(result):
            print(f"\n🔔 监测回调: {result.monitor_type} - {result.target}")
            if result.success and result.tweets:
                print(f"   发现 {len(result.tweets)} 条新推文")
                for tweet in result.tweets[:2]:  # 只显示前2条
                    print(f"   • {tweet.text[:80]}...")
            elif result.success:
                print("   没有发现新推文")
            else:
                print(f"   监测失败: {result.error_message}")
        
        monitor.add_result_callback(demo_callback)
        
        print("📱 正在初始化监测器...")
        if monitor.initialize():
            print("✅ 监测器初始化成功")
            
            print("\n🚀 开始演示监测（运行60秒）...")
            print("监测账号: @elonmusk")
            print("监测关键词: AI")
            print("检查间隔: 30秒")
            
            monitor.start_monitoring()
            
            # 运行60秒
            for i in range(60):
                time.sleep(1)
                if i % 10 == 0:
                    print(f"⏱️ 运行中... {i}/60秒")
            
            print("\n⏹️ 停止监测...")
            monitor.stop_monitoring()
            
            # 显示统计信息
            status = monitor.get_status()
            print(f"\n📊 监测统计:")
            print(f"   成功次数: {status['success_count']}")
            print(f"   错误次数: {status['error_count']}")
            print(f"   已见推文: {status['seen_tweets_count']}")
            
        else:
            print("❌ 监测器初始化失败")
        
        monitor.cleanup()
        print("\n✅ 监测演示完成")
        
    except Exception as e:
        print(f"❌ 监测演示过程中发生错误: {e}")


def demo_config_management():
    """演示配置管理"""
    print("\n⚙️ 演示配置管理")
    print("=" * 50)
    
    try:
        # 创建配置实例
        config = Config()
        
        print("📋 当前配置:")
        print(f"   监测账号: {config.get_accounts()}")
        print(f"   监测关键词: {config.get_keywords()}")
        print(f"   检查间隔: {config.get('monitor.check_interval')}秒")
        
        # 演示动态配置
        print("\n🔧 演示动态配置...")
        
        print("添加账号: demo_user")
        config.add_account("demo_user")
        
        print("添加关键词: 演示关键词")
        config.add_keyword("演示关键词")
        
        print("\n📋 更新后配置:")
        print(f"   监测账号: {config.get_accounts()}")
        print(f"   监测关键词: {config.get_keywords()}")
        
        # 移除演示数据
        config.remove_account("demo_user")
        config.remove_keyword("演示关键词")
        
        print("\n✅ 配置管理演示完成")
        
    except Exception as e:
        print(f"❌ 配置管理演示过程中发生错误: {e}")


def main():
    """主演示函数"""
    print("🐦 Twitter监测脚本功能演示")
    print("=" * 60)
    print("本演示将展示脚本的主要功能")
    print("注意: 演示需要网络连接和Chrome浏览器")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger('demo')
    
    try:
        # 询问用户要演示哪些功能
        print("\n请选择要演示的功能:")
        print("1. 基本爬取功能")
        print("2. 监测功能")
        print("3. 配置管理")
        print("4. 全部演示")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == "1":
            demo_basic_scraping()
        elif choice == "2":
            demo_monitoring()
        elif choice == "3":
            demo_config_management()
        elif choice == "4":
            demo_config_management()
            demo_basic_scraping()
            demo_monitoring()
        elif choice == "0":
            print("👋 退出演示")
            return
        else:
            print("❌ 无效选择")
            return
        
        print("\n🎉 演示完成！")
        print("如需了解更多功能，请查看 README.md 文件")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
