"""
MCP (Model Context Protocol) 服务器实现
提供标准化的Twitter爬虫接口
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MCPMessageType(Enum):
    """MCP消息类型"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"


class MCPMethod(Enum):
    """MCP方法类型"""
    INITIALIZE = "initialize"
    LIST_TOOLS = "tools/list"
    CALL_TOOL = "tools/call"
    LIST_RESOURCES = "resources/list"
    READ_RESOURCE = "resources/read"


@dataclass
class MCPTool:
    """MCP工具定义"""
    name: str
    description: str
    inputSchema: Dict[str, Any]


@dataclass
class MCPResource:
    """MCP资源定义"""
    uri: str
    name: str
    description: str
    mimeType: str


@dataclass
class MCPMessage:
    """MCP消息基类"""
    jsonrpc: str = "2.0"
    id: Optional[str] = None
    method: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    result: Optional[Any] = None
    error: Optional[Dict[str, Any]] = None


class MCPServer:
    """MCP服务器核心类"""
    
    def __init__(self):
        self.tools: Dict[str, MCPTool] = {}
        self.resources: Dict[str, MCPResource] = {}
        self.tool_handlers: Dict[str, callable] = {}
        self.initialized = False
        
        # 注册默认工具
        self._register_default_tools()
    
    def _register_default_tools(self):
        """注册默认的Twitter爬虫工具"""
        
        # 用户推文爬取工具
        user_tweets_tool = MCPTool(
            name="scrape_user_tweets",
            description="爬取指定用户的推文内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "username": {
                        "type": "string",
                        "description": "Twitter用户名（不包含@符号）"
                    },
                    "count": {
                        "type": "integer",
                        "description": "爬取推文数量",
                        "default": 20,
                        "minimum": 1,
                        "maximum": 100
                    },
                    "include_replies": {
                        "type": "boolean",
                        "description": "是否包含回复推文",
                        "default": False
                    },
                    "max_age_days": {
                        "type": "integer",
                        "description": "最大时间范围（天）",
                        "default": 30
                    }
                },
                "required": ["username"]
            }
        )
        
        # 关键词搜索工具
        keyword_tweets_tool = MCPTool(
            name="scrape_keyword_tweets",
            description="根据关键词搜索并爬取相关推文",
            inputSchema={
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "搜索关键词"
                    },
                    "count": {
                        "type": "integer",
                        "description": "爬取推文数量",
                        "default": 20,
                        "minimum": 1,
                        "maximum": 100
                    },
                    "language": {
                        "type": "string",
                        "description": "语言过滤（如：zh, en, ja）",
                        "default": None
                    },
                    "date_range": {
                        "type": "string",
                        "description": "时间范围（如：7d, 30d, 1y）",
                        "default": "30d"
                    }
                },
                "required": ["keyword"]
            }
        )
        
        # 推文分析工具
        analyze_tweets_tool = MCPTool(
            name="analyze_tweets",
            description="使用大模型分析推文内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "tweets": {
                        "type": "array",
                        "description": "推文数据数组",
                        "items": {"type": "object"}
                    },
                    "analysis_type": {
                        "type": "string",
                        "description": "分析类型",
                        "enum": ["sentiment", "summary", "topics", "all"],
                        "default": "all"
                    },
                    "language": {
                        "type": "string",
                        "description": "分析语言",
                        "default": "zh"
                    }
                },
                "required": ["tweets"]
            }
        )
        
        # 状态查询工具
        status_tool = MCPTool(
            name="get_scraper_status",
            description="获取爬虫状态和统计信息",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
        
        # 注册工具
        self.register_tool(user_tweets_tool)
        self.register_tool(keyword_tweets_tool) 
        self.register_tool(analyze_tweets_tool)
        self.register_tool(status_tool)
    
    def register_tool(self, tool: MCPTool):
        """注册MCP工具"""
        self.tools[tool.name] = tool
        logger.info(f"已注册工具: {tool.name}")
    
    def register_tool_handler(self, tool_name: str, handler: callable):
        """注册工具处理函数"""
        self.tool_handlers[tool_name] = handler
        logger.info(f"已注册工具处理器: {tool_name}")
    
    def register_resource(self, resource: MCPResource):
        """注册MCP资源"""
        self.resources[resource.uri] = resource
        logger.info(f"已注册资源: {resource.uri}")
    
    async def handle_message(self, message_data: str) -> str:
        """处理MCP消息"""
        try:
            # 解析消息
            message_dict = json.loads(message_data)
            message = MCPMessage(**message_dict)
            
            # 路由消息
            if message.method == MCPMethod.INITIALIZE.value:
                response = await self._handle_initialize(message)
            elif message.method == MCPMethod.LIST_TOOLS.value:
                response = await self._handle_list_tools(message)
            elif message.method == MCPMethod.CALL_TOOL.value:
                response = await self._handle_call_tool(message)
            elif message.method == MCPMethod.LIST_RESOURCES.value:
                response = await self._handle_list_resources(message)
            elif message.method == MCPMethod.READ_RESOURCE.value:
                response = await self._handle_read_resource(message)
            else:
                response = self._create_error_response(
                    message.id, -32601, f"未知方法: {message.method}"
                )
            
            return json.dumps(asdict(response), ensure_ascii=False, indent=2)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            return json.dumps({
                "jsonrpc": "2.0",
                "id": None,
                "error": {"code": -32700, "message": "解析错误"}
            }, ensure_ascii=False)
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            return json.dumps({
                "jsonrpc": "2.0", 
                "id": None,
                "error": {"code": -32603, "message": f"内部错误: {str(e)}"}
            }, ensure_ascii=False)
    
    async def _handle_initialize(self, message: MCPMessage) -> MCPMessage:
        """处理初始化请求"""
        self.initialized = True
        return MCPMessage(
            id=message.id,
            result={
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {"listChanged": True},
                    "resources": {"listChanged": True}
                },
                "serverInfo": {
                    "name": "Twitter MCP Scraper",
                    "version": "1.0.0",
                    "description": "Twitter爬虫MCP服务器"
                }
            }
        )
    
    async def _handle_list_tools(self, message: MCPMessage) -> MCPMessage:
        """处理工具列表请求"""
        tools_list = [asdict(tool) for tool in self.tools.values()]
        return MCPMessage(
            id=message.id,
            result={"tools": tools_list}
        )
    
    async def _handle_call_tool(self, message: MCPMessage) -> MCPMessage:
        """处理工具调用请求"""
        if not message.params:
            return self._create_error_response(
                message.id, -32602, "缺少参数"
            )
        
        tool_name = message.params.get("name")
        arguments = message.params.get("arguments", {})
        
        if tool_name not in self.tools:
            return self._create_error_response(
                message.id, -32601, f"未知工具: {tool_name}"
            )
        
        if tool_name not in self.tool_handlers:
            return self._create_error_response(
                message.id, -32603, f"工具处理器未注册: {tool_name}"
            )
        
        try:
            # 调用工具处理器
            handler = self.tool_handlers[tool_name]
            result = await handler(**arguments)
            
            return MCPMessage(
                id=message.id,
                result={
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(result, ensure_ascii=False, indent=2)
                        }
                    ]
                }
            )
        except Exception as e:
            logger.error(f"工具调用错误 {tool_name}: {e}")
            return self._create_error_response(
                message.id, -32603, f"工具执行错误: {str(e)}"
            )
    
    async def _handle_list_resources(self, message: MCPMessage) -> MCPMessage:
        """处理资源列表请求"""
        resources_list = [asdict(resource) for resource in self.resources.values()]
        return MCPMessage(
            id=message.id,
            result={"resources": resources_list}
        )
    
    async def _handle_read_resource(self, message: MCPMessage) -> MCPMessage:
        """处理资源读取请求"""
        if not message.params:
            return self._create_error_response(
                message.id, -32602, "缺少参数"
            )
        
        uri = message.params.get("uri")
        if uri not in self.resources:
            return self._create_error_response(
                message.id, -32601, f"未知资源: {uri}"
            )
        
        # 这里应该实现具体的资源读取逻辑
        return MCPMessage(
            id=message.id,
            result={
                "contents": [
                    {
                        "uri": uri,
                        "mimeType": self.resources[uri].mimeType,
                        "text": "资源内容"
                    }
                ]
            }
        )
    
    def _create_error_response(self, message_id: str, code: int, message: str) -> MCPMessage:
        """创建错误响应"""
        return MCPMessage(
            id=message_id,
            error={
                "code": code,
                "message": message
            }
        )


if __name__ == "__main__":
    # 测试MCP服务器
    server = MCPServer()
    print("MCP服务器初始化完成")
    print(f"已注册工具数量: {len(server.tools)}")
    for tool_name in server.tools:
        print(f"- {tool_name}")

