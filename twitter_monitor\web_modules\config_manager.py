"""
配置管理模块
提供监测配置的Web界面管理
"""

import streamlit as st
import yaml
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config):
        self.config = config
    
    def render(self):
        """渲染配置管理界面"""
        st.markdown('<h1 class="main-header">⚙️ 监测配置管理</h1>', unsafe_allow_html=True)
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📱 账号管理", "🔍 关键词管理", "⏱️ 监测设置", "🛡️ 反屏蔽配置"])
        
        with tab1:
            self.render_account_management()
        
        with tab2:
            self.render_keyword_management()
        
        with tab3:
            self.render_monitor_settings()
        
        with tab4:
            self.render_anti_block_settings()
        
        # 配置操作按钮
        self.render_config_actions()
    
    def render_account_management(self):
        """渲染账号管理"""
        st.markdown("### 📱 Twitter账号监测管理")
        
        # 当前监测账号
        current_accounts = self.config.get_accounts()
        
        st.markdown("#### 当前监测账号")
        if current_accounts:
            for i, account in enumerate(current_accounts):
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.text(f"@{account}")
                with col2:
                    if st.button("🗑️ 删除", key=f"del_account_{i}"):
                        if self.remove_account(account):
                            st.success(f"✅ 已删除账号 @{account}")
                            st.rerun()
        else:
            st.info("暂无监测账号")
        
        st.markdown("---")
        
        # 添加新账号
        st.markdown("#### 添加新账号")
        
        col1, col2 = st.columns([3, 1])
        with col1:
            new_account = st.text_input(
                "Twitter用户名",
                placeholder="输入用户名（不含@符号）",
                help="例如: 马斯克的账号, 某公司账号"
            )
        
        with col2:
            st.markdown("<br>", unsafe_allow_html=True)  # 对齐按钮
            if st.button("➕ 添加账号"):
                if new_account:
                    if self.add_account(new_account):
                        st.rerun()
                else:
                    st.error("请输入账号名")
        
        # 批量添加
        st.markdown("#### 批量添加账号")
        batch_accounts = st.text_area(
            "批量添加",
            placeholder="每行一个账号名，例如:\n账号1\n账号2\n账号3",
            help="每行输入一个Twitter用户名"
        )
        
        if st.button("📥 批量添加"):
            if batch_accounts:
                accounts = [acc.strip() for acc in batch_accounts.split('\n') if acc.strip()]
                added_count = 0
                for account in accounts:
                    if self.add_account(account):
                        added_count += 1
                
                if added_count > 0:
                    st.success(f"✅ 成功添加 {added_count} 个账号")
                    st.rerun()
                else:
                    st.warning("⚠️ 没有添加新账号")
    
    def render_keyword_management(self):
        """渲染关键词管理"""
        st.markdown("### 🔍 关键词监测管理")
        
        # 当前监测关键词
        current_keywords = self.config.get_keywords()
        
        st.markdown("#### 当前监测关键词")
        if current_keywords:
            for i, keyword in enumerate(current_keywords):
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.text(f"🔍 {keyword}")
                with col2:
                    if st.button("🗑️ 删除", key=f"del_keyword_{i}"):
                        if self.remove_keyword(keyword):
                            st.success(f"✅ 已删除关键词 {keyword}")
                            st.rerun()
        else:
            st.info("暂无监测关键词")
        
        st.markdown("---")
        
        # 添加新关键词
        st.markdown("#### 添加新关键词")
        
        col1, col2 = st.columns([3, 1])
        with col1:
            new_keyword = st.text_input(
                "关键词",
                placeholder="输入要监测的关键词",
                help="例如: 人工智能, 科技, 新闻"
            )
        
        with col2:
            st.markdown("<br>", unsafe_allow_html=True)
            if st.button("➕ 添加关键词"):
                if new_keyword:
                    if self.add_keyword(new_keyword):
                        st.success(f"✅ 已添加关键词 {new_keyword}")
                        st.rerun()
                else:
                    st.error("请输入关键词")
        
        # 批量添加
        st.markdown("#### 批量添加关键词")
        batch_keywords = st.text_area(
            "批量添加",
            placeholder="每行一个关键词，例如:\n人工智能\n科技\n新闻",
            help="每行输入一个关键词"
        )
        
        if st.button("📥 批量添加关键词"):
            if batch_keywords:
                keywords = [kw.strip() for kw in batch_keywords.split('\n') if kw.strip()]
                added_count = 0
                for keyword in keywords:
                    if self.add_keyword(keyword):
                        added_count += 1
                
                if added_count > 0:
                    st.success(f"✅ 成功添加 {added_count} 个关键词")
                    st.rerun()
                else:
                    st.warning("⚠️ 没有添加新关键词")
    
    def render_monitor_settings(self):
        """渲染监测设置"""
        st.markdown("### ⏱️ 监测频率和参数设置")
        
        # 检查间隔设置
        current_interval = self.config.get('monitor.check_interval', 300)
        
        col1, col2 = st.columns(2)
        
        with col1:
            new_interval = st.number_input(
                "检查间隔（秒）",
                min_value=60,
                max_value=3600,
                value=current_interval,
                step=60,
                help="监测检查的时间间隔，建议不少于300秒（5分钟）"
            )
            
            # 预设选项
            st.markdown("**快速设置:**")
            col_a, col_b, col_c = st.columns(3)
            with col_a:
                if st.button("5分钟"):
                    new_interval = 300
            with col_b:
                if st.button("10分钟"):
                    new_interval = 600
            with col_c:
                if st.button("30分钟"):
                    new_interval = 1800
        
        with col2:
            # 最大推文数设置
            current_max_tweets = self.config.get('monitor.max_tweets', 20)
            new_max_tweets = st.number_input(
                "每次最大推文数",
                min_value=5,
                max_value=100,
                value=current_max_tweets,
                step=5,
                help="每次监测获取的最大推文数量"
            )
            
            # 数据目录设置
            current_data_dir = self.config.get('monitor.data_dir', 'data')
            new_data_dir = st.text_input(
                "数据存储目录",
                value=current_data_dir,
                help="监测数据的存储目录"
            )
        
        # 保存监测设置
        if st.button("💾 保存监测设置"):
            try:
                self.config.set('monitor.check_interval', new_interval)
                self.config.set('monitor.max_tweets', new_max_tweets)
                self.config.set('monitor.data_dir', new_data_dir)
                self.config.save_config()
                
                st.success("✅ 监测设置已保存")
                
                # 更新session state以触发监测器重新配置
                st.session_state.config_changed = True
                
            except Exception as e:
                st.error(f"❌ 保存设置失败: {e}")
    
    def render_anti_block_settings(self):
        """渲染反屏蔽设置"""
        st.markdown("### 🛡️ 反屏蔽策略配置")
        
        # 请求频率控制
        st.markdown("#### 📊 请求频率控制")
        
        col1, col2 = st.columns(2)
        
        with col1:
            current_rpm = self.config.get('anti_block.requests_per_minute', 30)
            new_rpm = st.number_input(
                "每分钟请求数",
                min_value=10,
                max_value=120,
                value=current_rpm,
                help="每分钟允许的最大请求数"
            )
        
        with col2:
            current_burst = self.config.get('anti_block.burst_limit', 5)
            new_burst = st.number_input(
                "突发请求限制",
                min_value=3,
                max_value=20,
                value=current_burst,
                help="短时间内允许的突发请求数"
            )
        
        # 代理设置
        st.markdown("#### 🔒 代理配置")
        
        current_proxies = self.config.get('anti_block.proxies', [])
        
        # 显示当前代理
        if current_proxies:
            st.markdown("**当前代理列表:**")
            for i, proxy in enumerate(current_proxies):
                col1, col2 = st.columns([4, 1])
                with col1:
                    st.text(proxy)
                with col2:
                    if st.button("🗑️", key=f"del_proxy_{i}"):
                        self.remove_proxy(i)
                        st.rerun()
        else:
            st.info("暂未配置代理")
        
        # 添加新代理
        st.markdown("**添加新代理:**")
        new_proxy = st.text_input(
            "代理地址",
            placeholder="http://代理地址:8080 或 socks5://用户名:密码@代理地址:1080",
            help="支持HTTP/HTTPS/SOCKS代理，格式: 协议://[用户名:密码@]主机:端口"
        )
        
        if st.button("➕ 添加代理"):
            if new_proxy:
                self.add_proxy(new_proxy)
                st.rerun()
        
        # 批量添加代理
        batch_proxies = st.text_area(
            "批量添加代理",
            placeholder="每行一个代理地址",
            help="每行输入一个代理地址"
        )
        
        if st.button("📥 批量添加代理"):
            if batch_proxies:
                proxies = [p.strip() for p in batch_proxies.split('\n') if p.strip()]
                for proxy in proxies:
                    self.add_proxy(proxy)
                st.success(f"✅ 添加了 {len(proxies)} 个代理")
                st.rerun()
        
        # 保存反屏蔽设置
        if st.button("💾 保存反屏蔽设置"):
            try:
                self.config.set('anti_block.requests_per_minute', new_rpm)
                self.config.set('anti_block.burst_limit', new_burst)
                self.config.save_config()
                
                st.success("✅ 反屏蔽设置已保存")
                st.session_state.config_changed = True
                
            except Exception as e:
                st.error(f"❌ 保存设置失败: {e}")
    
    def render_config_actions(self):
        """渲染配置操作"""
        st.markdown("---")
        st.markdown("### 🔧 配置操作")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("💾 保存所有配置"):
                self.save_all_config()
        
        with col2:
            if st.button("🔄 重载配置"):
                self.reload_config()
        
        with col3:
            if st.button("📋 导出配置"):
                self.export_config()
        
        with col4:
            if st.button("📥 导入配置"):
                self.import_config()
    
    def add_account(self, account: str) -> bool:
        """添加账号"""
        try:
            # 验证账号名
            account = account.strip().lstrip('@')
            if not account:
                st.error("账号名不能为空")
                return False
            
            # 检查是否已存在
            if account in self.config.get_accounts():
                st.warning(f"账号 @{account} 已存在")
                return False
            
            self.config.add_account(account)
            st.success(f"✅ 已添加账号: @{account}")
            return True
            
        except Exception as e:
            st.error(f"❌ 添加账号失败: {e}")
            return False
    
    def remove_account(self, account: str):
        """移除账号"""
        try:
            self.config.remove_account(account)
            st.success(f"✅ 已移除账号: @{account}")
        except Exception as e:
            st.error(f"❌ 移除账号失败: {e}")
    
    def add_keyword(self, keyword: str) -> bool:
        """添加关键词"""
        try:
            keyword = keyword.strip()
            if not keyword:
                st.error("关键词不能为空")
                return False
            
            if keyword in self.config.get_keywords():
                st.warning(f"关键词 '{keyword}' 已存在")
                return False
            
            self.config.add_keyword(keyword)
            st.success(f"✅ 已添加关键词: {keyword}")
            return True
            
        except Exception as e:
            st.error(f"❌ 添加关键词失败: {e}")
            return False
    
    def remove_keyword(self, keyword: str):
        """移除关键词"""
        try:
            self.config.remove_keyword(keyword)
            st.success(f"✅ 已移除关键词: {keyword}")
        except Exception as e:
            st.error(f"❌ 移除关键词失败: {e}")
    
    def add_proxy(self, proxy: str):
        """添加代理"""
        try:
            current_proxies = self.config.get('anti_block.proxies', [])
            if proxy not in current_proxies:
                current_proxies.append(proxy)
                self.config.set('anti_block.proxies', current_proxies)
                st.success(f"✅ 已添加代理: {proxy}")
            else:
                st.warning("代理已存在")
        except Exception as e:
            st.error(f"❌ 添加代理失败: {e}")
    
    def remove_proxy(self, index: int):
        """移除代理"""
        try:
            current_proxies = self.config.get('anti_block.proxies', [])
            if 0 <= index < len(current_proxies):
                removed_proxy = current_proxies.pop(index)
                self.config.set('anti_block.proxies', current_proxies)
                st.success(f"✅ 已移除代理: {removed_proxy}")
        except Exception as e:
            st.error(f"❌ 移除代理失败: {e}")
    
    def save_all_config(self):
        """保存所有配置"""
        try:
            self.config.save_config()
            st.success("✅ 所有配置已保存")
            st.session_state.config_changed = True
        except Exception as e:
            st.error(f"❌ 保存配置失败: {e}")
    
    def reload_config(self):
        """重载配置"""
        try:
            self.config.load_config()
            st.success("✅ 配置已重载")
            st.rerun()
        except Exception as e:
            st.error(f"❌ 重载配置失败: {e}")
    
    def export_config(self):
        """导出配置"""
        try:
            config_dict = self.config.to_dict()
            config_yaml = yaml.dump(config_dict, default_flow_style=False, allow_unicode=True)
            
            st.download_button(
                label="📋 下载配置文件",
                data=config_yaml,
                file_name="twitter_monitor_config.yaml",
                mime="application/x-yaml"
            )
            
        except Exception as e:
            st.error(f"❌ 导出配置失败: {e}")
    
    def import_config(self):
        """导入配置"""
        uploaded_file = st.file_uploader(
            "选择配置文件",
            type=['yaml', 'yml'],
            help="上传YAML格式的配置文件"
        )
        
        if uploaded_file is not None:
            try:
                config_content = uploaded_file.read().decode('utf-8')
                config_dict = yaml.safe_load(config_content)
                
                # 这里可以添加配置验证逻辑
                st.success("✅ 配置文件已导入")
                st.json(config_dict)
                
                if st.button("确认导入"):
                    # 实际导入配置的逻辑
                    pass
                    
            except Exception as e:
                st.error(f"❌ 导入配置失败: {e}")
