#!/usr/bin/env python3
"""
Twitter监测脚本安装器
自动安装依赖和配置环境
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_header():
    """打印安装器标题"""
    print("🐦 Twitter监测脚本安装器")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print("=" * 50)


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        print("请升级Python后重试")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def install_pip_packages():
    """安装pip包"""
    print("\n📦 安装Python依赖包...")
    
    # 核心依赖包
    packages = [
        "selenium>=4.15.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0",
        "PyYAML>=6.0.1",
        "fake-useragent>=1.4.0",
        "pandas>=2.1.0",
        "python-dateutil>=2.8.2",
        "webdriver-manager>=4.0.0"
    ]
    
    for package in packages:
        try:
            print(f"  安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"  ✅ {package} 安装成功")
            else:
                print(f"  ❌ {package} 安装失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {package} 安装超时")
            return False
        except Exception as e:
            print(f"  ❌ {package} 安装错误: {e}")
            return False
    
    print("✅ 所有Python依赖包安装完成")
    return True


def install_chrome_driver():
    """安装ChromeDriver"""
    print("\n🌐 配置Chrome WebDriver...")
    
    try:
        from selenium import webdriver
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        
        # 使用webdriver-manager自动下载ChromeDriver
        print("  正在下载ChromeDriver...")
        driver_path = ChromeDriverManager().install()
        print(f"  ✅ ChromeDriver已安装到: {driver_path}")
        
        # 测试ChromeDriver
        print("  测试ChromeDriver...")
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://www.google.com")
        driver.quit()
        
        print("  ✅ ChromeDriver测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ ChromeDriver配置失败: {e}")
        print("  请手动安装Chrome浏览器和ChromeDriver")
        return False


def create_project_structure():
    """创建项目目录结构"""
    print("\n📁 创建项目目录结构...")
    
    directories = [
        "data",
        "data/exports",
        "logs",
        "config"
    ]
    
    base_path = Path(__file__).parent
    
    for directory in directories:
        dir_path = base_path / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"  ✅ 创建目录: {directory}")
    
    print("✅ 项目目录结构创建完成")


def create_sample_config():
    """创建示例配置文件"""
    print("\n⚙️ 创建示例配置文件...")
    
    config_path = Path(__file__).parent / "config" / "config.yaml"
    
    if config_path.exists():
        print("  ℹ️ 配置文件已存在，跳过创建")
        return True
    
    sample_config = """# Twitter账号监测脚本配置文件

# 监测配置
monitor:
  # 要监测的Twitter账号列表
  accounts:
    - "elonmusk"
    - "openai"
  
  # 要监测的关键词列表
  keywords:
    - "人工智能"
    - "AI"
    - "ChatGPT"
  
  # 检查间隔（秒）
  check_interval: 300  # 5分钟
  
  # 每次获取的最大推文数
  max_tweets: 20
  
  # 数据存储目录
  data_dir: "data"

# 爬虫配置
scraper:
  # 请求延迟范围（秒）
  request_delay: [2, 5]
  
  # 最大重试次数
  max_retries: 3
  
  # 请求超时时间（秒）
  timeout: 10
  
  # 是否使用无头模式
  headless: true

# 反屏蔽配置
anti_block:
  # 每分钟请求数限制
  requests_per_minute: 30
  
  # 突发请求限制
  burst_limit: 5
  
  # 代理列表（可选）
  proxies: []

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/twitter_monitor.log"
  max_size: 10
  backup_count: 5
  console: true

# 输出配置
output:
  print_new_tweets: true
  save_json: true
  save_csv: false
  max_text_length: 200
"""
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(sample_config)
        print(f"  ✅ 示例配置文件已创建: {config_path}")
        return True
    except Exception as e:
        print(f"  ❌ 创建配置文件失败: {e}")
        return False


def create_startup_scripts():
    """创建启动脚本"""
    print("\n🚀 创建启动脚本...")
    
    base_path = Path(__file__).parent
    
    # Windows批处理文件
    if platform.system() == "Windows":
        bat_content = """@echo off
echo Starting Twitter Monitor...
python main.py
pause
"""
        bat_path = base_path / "start.bat"
        try:
            with open(bat_path, 'w', encoding='utf-8') as f:
                f.write(bat_content)
            print(f"  ✅ Windows启动脚本: {bat_path}")
        except Exception as e:
            print(f"  ❌ 创建Windows启动脚本失败: {e}")
    
    # Unix shell脚本
    else:
        sh_content = """#!/bin/bash
echo "Starting Twitter Monitor..."
python3 main.py
"""
        sh_path = base_path / "start.sh"
        try:
            with open(sh_path, 'w', encoding='utf-8') as f:
                f.write(sh_content)
            
            # 添加执行权限
            os.chmod(sh_path, 0o755)
            print(f"  ✅ Unix启动脚本: {sh_path}")
        except Exception as e:
            print(f"  ❌ 创建Unix启动脚本失败: {e}")


def run_test():
    """运行测试"""
    print("\n🧪 运行安装测试...")
    
    try:
        # 测试导入核心模块
        print("  测试模块导入...")
        
        sys.path.insert(0, str(Path(__file__).parent))
        
        from config import Config
        from core import TwitterScraper, AntiBlockManager
        from utils import setup_logger
        
        print("  ✅ 核心模块导入成功")
        
        # 测试配置加载
        print("  测试配置加载...")
        config = Config()
        print(f"  ✅ 配置加载成功，监测账号: {len(config.get_accounts())}个")
        
        # 测试日志设置
        print("  测试日志设置...")
        logger = setup_logger('test', config.logging_config)
        logger.info("测试日志消息")
        print("  ✅ 日志设置成功")
        
        print("✅ 所有测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False


def print_completion_message():
    """打印完成消息"""
    print("\n" + "=" * 50)
    print("🎉 Twitter监测脚本安装完成！")
    print("=" * 50)
    print("\n📋 下一步操作:")
    print("1. 编辑 config/config.yaml 文件，配置要监测的账号和关键词")
    print("2. 运行 python main.py 启动监测")
    print("3. 或者运行 python start.py 使用启动器")
    
    if platform.system() == "Windows":
        print("4. 或者双击 start.bat 文件启动")
    else:
        print("4. 或者运行 ./start.sh 启动")
    
    print("\n⚠️ 注意事项:")
    print("- 请确保已安装Chrome浏览器")
    print("- 合理设置监测频率，避免被封IP")
    print("- 遵守Twitter服务条款")
    print("- 仅监测公开内容")
    
    print("\n📖 更多信息请查看 README.md 文件")
    print("=" * 50)


def main():
    """主安装函数"""
    print_header()
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖包
    if not install_pip_packages():
        print("❌ 依赖包安装失败")
        return False
    
    # 配置ChromeDriver
    install_chrome_driver()  # 不强制要求成功
    
    # 创建项目结构
    create_project_structure()
    
    # 创建配置文件
    create_sample_config()
    
    # 创建启动脚本
    create_startup_scripts()
    
    # 运行测试
    if not run_test():
        print("⚠️ 测试失败，但安装可能仍然成功")
    
    # 打印完成消息
    print_completion_message()
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        sys.exit(1)
